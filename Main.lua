local AlphaProtocol = LibStub("AceAddon-3.0"):GetAddon("FriendshipIsMagic")
local Main = AlphaProtocol:NewModule("Main", "AceConsole-3.0")

local defaults = {
	profile = {
		-- Другие настройки...
		mainButtonPosition = {
			point = "TOP",
			relativePoint = "TOP",
			xOfs = 0,
			yOfs = 0,
		},
		showMainMenuButton = true,
		enableAutoSync2 = true, -- Автоматическая синхронизация включена по умолчанию
		autoSyncLoot2 = true, -- Установка всех параметров автосинхронизации на true по умолчанию
		autoSyncNotes2 = true,
		autoSyncTimestamps2 = true,
		autoSendUpdates2 = true,
		autoSendClearAdd2 = true
	},
}

function Main:OnInitialize()
	self.db = LibStub("AceDB-3.0"):New("FriendshipIsMagicDB", defaults)
	AlphaProtocol.db = self.db

	self:CheckOrCreateDataStructures()

	-- Инициализируем только необходимые модули до проверки пароля
	AlphaProtocol.InterfaceModule = AlphaProtocol:GetModule("InterfaceModule")
	
	-- Регистрация команд чата
	self:RegisterChatCommand("printRDB", "PrintRaidInfoByRaidID")
	self:RegisterChatCommand("debugraid", "DebugCurrentRaid")
	self:RegisterChatCommand("listraids", "PrintRaidConfigs")
	self:RegisterChatCommand("syncstatus", "SyncAllStatuses")

	-- Проверяем, был ли ранее введен правильный пароль и валиден ли он
	if self.db.global.i then
		local calendarTime = C_DateAndTime.GetCurrentCalendarTime()
		if not calendarTime or not calendarTime.year or calendarTime.year < 2000 then
			print("Ожидание синхронизации календаря...")
			C_Timer.After(1, function()
				self:OnInitialize()
			end)
			return
		end
		
		local currentHash = AlphaProtocol.InterfaceModule:lolcheckPassword()
		if currentHash and currentHash ~= 6 then
			self:InitializeAfterPassword()
		elseif self.db.global.nexti then
			-- Проверяем, подходит ли пароль следующей недели как текущий
			local nextWeekHash = AlphaProtocol.InterfaceModule:lolcheckPassword()
			if nextWeekHash and nextWeekHash == true then
				-- Переносим пароль следующей недели в текущую
				self.db.global.i = self.db.global.nexti
				self.db.global.nexti = nil
				print("Пароль обновлен на следующую неделю.")
				self:InitializeAfterPassword()
				return
			end
			-- Если ни один пароль не подходит, очищаем оба
			self.db.global.i = nil
			self.db.global.nexti = nil
			print("Пароль устарел. Пожалуйста, введите новый пароль для текущей недели.")
		else
			self.db.global.i = nil
			print("Пароль устарел. Пожалуйста, введите новый пароль для текущей недели.")
		end
	else
		print("Введите /fim password <пароль> для начала работы")
	end
end

-- Функция для инициализации остальных модулей после проверки пароля
function Main:InitializeAfterPassword()
	AlphaProtocol.ManagmentModule = AlphaProtocol:GetModule("ManagmentModule")
	AlphaProtocol.EventsModule = AlphaProtocol:GetModule("EventsModule")
	AlphaProtocol.ExportModule = AlphaProtocol:GetModule("ExportModule")
	AlphaProtocol.OtherModule = AlphaProtocol:GetModule("OtherModule")
	AlphaProtocol.LootModule = AlphaProtocol:GetModule("LootModule")
	AlphaProtocol.RaidManagerModule = AlphaProtocol:GetModule("RaidManagerModule")
	AlphaProtocol.TaskConfigModule = AlphaProtocol:GetModule("TaskConfigModule")
	AlphaProtocol.StatusSyncModule = AlphaProtocol:GetModule("StatusSyncModule")

	-- Загрузка игровых данных
	self:LoadPlayersData()

	-- Удаление старых данных
	self:RemoveOldRaidData()

	print("Привет {RLNAME}, хорошего дня!")
end

-- Инициализация DB, если это не было сделано ранее
function Main:CheckOrCreateDataStructures()
	-- Переносим данные базы данных, если они есть
	if not self.db.global then
		self.db.global = {}
	end

	if not self.db.global.RaidDB then
		self.db.global.RaidDB = {}
	end

	if self.db.global.passwordChecked == nil then
		self.db.global.passwordChecked = false
	end
	if not Main.db.profile.minimap or type(Main.db.profile.minimap) ~= "table" then
		Main.db.profile.minimap = {
			hide = false,
		}
	end
end

-- Функция для установки lastRaidID с уведомлением об изменениях
function Main:SetLastRaidID(newRaidID)
	if self.db.global.lastRaidID ~= newRaidID then
		self.db.global.lastRaidID = newRaidID
		-- Обновляем интерфейс
		if AlphaProtocol.InterfaceModule and AlphaProtocol.InterfaceModule.UpdateMainWindowRaidInfo then
			AlphaProtocol.InterfaceModule:UpdateMainWindowRaidInfo()
		end
		-- Отмечаем изменения для синхронизации
	end
end

-- База данных
function Main:RaidList(
	raidID,
	raidDate,
	raidTime,
	raidTeam,
	playerID,
	playerName,
	serverName,
	playerTask,
	playerInfo,
	rlinfo,
	declinedTimestamp,
	noexistTimestamp,
	busyTimestamp,
	leaveTimestamp,
	bossKills,
	expiredTimestamps,
	doomsDay,
	isVisible
)
	self:CheckOrCreateDataStructures()

	local raidDB = self.db.global.RaidDB

	-- Создаем или обновляем запись о рейде
	if not raidDB[raidID] then
		raidDB[raidID] = {
			raidDate = raidDate or "Unknown",
			raidTime = raidTime or "Unknown",
			raidTeam = raidTeam or "Default",
			doomsDay = doomsDay,
			players = {},
			version = 1  -- Добавляем версию
		}
	end

	-- Обновление информации о последнем RaidID
	self:SetLastRaidID(raidID)

	-- Создаем или обновляем информацию об игроке
	local player = raidDB[raidID].players[playerID] or {
		declinedTimestamps = {},
		noexistTimestamps = {},
		busyTimestamps = {},
		leaveTimestamps = {},
		expiredTimestamps = {},
		bossKills = {}
	}

	-- Обновляем основную информацию об игроке
	player.playerID = playerID
	player.playerName = playerName or ""
	player.serverName = serverName or ""
	player.playerTask = playerTask or ""
	player.playerInfo = playerInfo or ""
	player.rlinfo = rlinfo or ""
	player.isVisible = isVisible or true

	-- Добавляем временные метки, если они есть
	if declinedTimestamp then
		table.insert(player.declinedTimestamps, declinedTimestamp)
	end
	if noexistTimestamp then
		table.insert(player.noexistTimestamps, noexistTimestamp)
	end
	if busyTimestamp then
		table.insert(player.busyTimestamps, busyTimestamp)
	end
	if leaveTimestamp then
		table.insert(player.leaveTimestamps, leaveTimestamp)
	end
	if expiredTimestamps then
		table.insert(player.expiredTimestamps, expiredTimestamps)
	end

	-- Обновляем данные об убийствах боссов
	if bossKills then
		player.bossKills = bossKills
	end

	-- Сохраняем обновленную информацию об игроке
	raidDB[raidID].players[playerID] = player
end

-- Восстанавливает информацию в оперативной памяти.
function Main:LoadPlayersData()
	-- Обнуляем список игроков для приглашения, чтобы загрузить только последние данные
	AlphaProtocol.ManagmentModule.playersToInvite = {}

	local lastRaidID = self.db.global.lastRaidID
	if lastRaidID and self.db.global.RaidDB[lastRaidID] then
		local savedRaid = self.db.global.RaidDB[lastRaidID]
		for playerID, playerData in pairs(savedRaid.players) do
			-- Построение полного имени игрока, включая сервер, если он указан
			local fullName = playerData.playerName
			if playerData.serverName and playerData.serverName ~= "" then
				fullName = fullName .. "-" .. playerData.serverName
			end

			-- Добавление игрока в список для приглашений
			table.insert(AlphaProtocol.ManagmentModule.playersToInvite, {
				playerID = playerID,
				name = fullName,
				Task = playerData.playerTask or "",
				info = playerData.playerInfo or "",
				rlinfo = playerData.rlinfo or "",
				isVisible = playerData.isVisible or true,
				isHide = playerData.isHide, -- Загружаем статус скрытия
				status = playerData.status or "", -- Загружаем статус игрока
				declinedTimestamps = playerData.declinedTimestamps or {},
				noexistTimestamps = playerData.noexistTimestamps or {},
				busyTimestamps = playerData.busyTimestamps or {},
				leaveTimestamps = playerData.leaveTimestamps or {},
				expiredTimestamps = playerData.expiredTimestamps or {},
			--	enemyTimestamps = playerData.enemyTimestamps or {},
				combatTimestamps = playerData.combatTimestamps or {},
				bossKills = playerData.bossKills or {}, -- Добавляем информацию об убийствах боссов
				pilot = playerData.pilot or "", -- Загружаем информацию о пилоте
				class = playerData.class or "", -- Загружаем информацию о классе
			})
		end
		--------print("Данные загружены для последнего рейда: " .. lastRaidID)
	else
		----print("ID последнего рейда не задан или рейд отсутствует в базе данных.")
	end
	AlphaProtocol.ManagmentModule:UpdateMainWindow()
	AlphaProtocol.InterfaceModule:UpdateMainWindowRaidInfo()
end

-- Синхронизация playersToInvite с RaidDB (просто вызвать в нужном месте для сохранения)
function Main:SavePlayersToRaidDB()
	-- Сохраняем только в текущий активный рейд (lastRaidID)
	local raidID = self.db.global.lastRaidID
	if not raidID or not self.db.global.RaidDB[raidID] then
		return
	end
	
	local raidData = self.db.global.RaidDB[raidID]
	local playersInRaidDB = raidData.players

	-- Обновление или добавление информации об игроках из playersToInvite
	for _, playerInfo in ipairs(AlphaProtocol.ManagmentModule.playersToInvite) do
		local playerID = playerInfo.playerID
		local playerData = playersInRaidDB[playerID] or {}

		-- Разделяем полное имя на имя и сервер
		local name, server = playerInfo.name:match("^(.-)%-(.+)$")
		playerData.playerName = name or playerInfo.name  -- если нет дефиса, используем полное имя
		playerData.serverName = server                   -- если нет дефиса, будет nil

		playerData.playerTask = playerInfo.Task
		playerData.playerInfo = playerInfo.info
		playerData.rlinfo = playerInfo.rlinfo
		playerData.isVisible = playerInfo.isVisible or true
		playerData.isHide = playerInfo.isHide -- Сохраняем статус скрытия
		playerData.status = playerInfo.status -- Сохраняем статус игрока
		playerData.bossKills = playerInfo.bossKills or {}
		playerData.declinedTimestamps = playerInfo.declinedTimestamps or {}
		playerData.noexistTimestamps = playerInfo.noexistTimestamps or {}
		playerData.busyTimestamps = playerInfo.busyTimestamps or {}
		playerData.leaveTimestamps = playerInfo.leaveTimestamps or {}
		playerData.expiredTimestamps = playerInfo.expiredTimestamps or {}
	--	playerData.enemyTimestamps = playerInfo.enemyTimestamps or {}
		playerData.combatTimestamps = playerInfo.combatTimestamps or {}
		playerData.pilot = playerInfo.pilot or "" -- Сохраняем информацию о пилоте
		playerData.class = playerInfo.class or "" -- Сохраняем информацию о классе

		playersInRaidDB[playerID] = playerData
	end
end

-- Функция для удаления устаревших данных о рейде через 30 дней
function Main:RemoveOldRaidData()
	local currentTime = time() -- Получаем текущее время в секундах Unix Timestamp
	----print("Текущее время: ", currentTime)

	for raidID, raidData in pairs(self.db.global.RaidDB) do
		local raidTimestamp = raidData.doomsDay
		----print("Временная метка рейда: ", raidTimestamp)

		if raidTimestamp then
			local timeDifference = (currentTime - raidTimestamp) / (60 * 60 * 24) -- Разница в днях
			----print("Разница в днях: ", timeDifference)

			if timeDifference > 30 then -- Условие для удаления данных, которым больше 30 дней
				----print("Удаляю данные рейда с ID: ", raidID)
				self.db.global.RaidDB[raidID] = nil -- Удаление устаревших данных о рейде
			end
		end
	end
end

-- Функция для отладки текущего рейда
function Main:DebugCurrentRaid()
    local lastRaidID = self.db.global.lastRaidID
    if not lastRaidID then
       --print("Нет активного рейда")
        return
    end

    local raidData = self.db.global.RaidDB[lastRaidID]
    if not raidData then
       --print("Данные рейда не найдены для ID:", lastRaidID)
        return
    end

    -- Вывод основной информации о рейде
   --print("=== Информация о текущем рейде ===")
   --print("ID рейда:", lastRaidID)
   --print("Дата рейда:", raidData.raidDate)
   --print("Время рейда:", raidData.raidTime)
   --print("Команда рейда:", raidData.raidTeam)
   --print("Timestamp создания:", raidData.doomsDay)
    
    -- Вывод информации о игроках из БД
   print("\n=== Игроки в базе данных ===")
    for playerID, playerData in pairs(raidData.players) do
       print(string.format("\nИгрок [%s]:", playerID))
       print("  Имя:", playerData.playerName)
       print("  Сервер:", playerData.serverName or "нет")
       print("  Полное имя в БД:", playerData.playerName .. (playerData.serverName and "-" .. playerData.serverName or ""))
       --print("  Задача:", playerData.playerTask or "нет")
       --print("  Инфо:", playerData.playerInfo or "нет")
       --print("  РЛ инфо:", playerData.rlinfo or "нет")
        if playerData.items then
           --print("  Полученные предметы:")
            for _, item in ipairs(playerData.items) do
               --print("    -", item)
            end
        end
    end

    -- Вывод информации из списка playersToInvite
   --print("\n=== Игроки в списке приглашений ===")
    for _, playerInfo in ipairs(AlphaProtocol.ManagmentModule.playersToInvite) do
       --print(string.format("\nИгрок [%s]:", playerInfo.playerID))
       --print("  Полное имя:", playerInfo.name)
       --print("  Задача:", playerInfo.Task or "нет")
       --print("  Инфо:", playerInfo.info or "нет")
       --print("  РЛ инфо:", playerInfo.rlinfo or "нет")
       --print("  Видимость:", playerInfo.isVisible and "да" or "нет")
        if playerInfo.items then
           --print("  Полученные предметы:")
            for _, item in ipairs(playerInfo.items) do
               --print("    -", item)
            end
        end
    end
end

-- Функция для вывода списка рейдов из базы данных
function Main:PrintRaidConfigs()
   --print("=== Список рейдов в базе данных ===")
    if not self.db.global.RaidConfigs then
       --print("База данных рейдов пуста")
        return
    end

    for raidID, raidConfig in pairs(self.db.global.RaidConfigs) do
       --print(string.format("\nРейд: %s (ID: %s)", raidConfig.name, raidID))
       --print("MapID:", raidConfig.mapID)
       --print("Боссы:")
        for i, boss in ipairs(raidConfig.bosses) do
           --print(string.format("  %d. %s (ID: %s)", i, boss.name, boss.id))
        end
    end
end

-- Функция для вызова ручной синхронизации статусов
function Main:SyncAllStatuses()
    if not AlphaProtocol.StatusSyncModule then
        print("Модуль синхронизации статусов не инициализирован.")
        return
    end
    
    AlphaProtocol.StatusSyncModule:ForceSyncAllStatuses()
end

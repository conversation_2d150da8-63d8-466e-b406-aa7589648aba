local AlphaProtocol = LibStub("AceAddon-3.0"):GetAddon("FriendshipIsMagic")
local OtherModule = AlphaProtocol:NewModule("OtherModule", "AceConsole-3.0", "AceEvent-3.0")
local LDBIcon = LibStub("LibDBIcon-1.0")
local Main = AlphaProtocol:GetModule("Main")

-- Version control constants
local VERSION_SEPARATOR = "#V#"
local COMMENT_SEPARATOR = "#&#"

-- Добавляем переменные для отслеживания времени последнего обновления буфера
local lastUpdateTime = {}
local BUFFER_TIMEOUT = 10 -- Тайм-аут в секундах

-- Helper function for string splitting
local function string_split(str, sep)
	local fields = {}
	local pattern = string.format("([^%s]+)", sep)
	str:gsub(pattern, function(c) fields[#fields+1] = c end)
	return fields
end

Main.tempSettings = {
	allowMoveMainMenuButton = false, -- Начальное состояние
}

function OtherModule:OnEnable()
	self:RegisterEvent("VARIABLES_LOADED", "OnVariablesLoaded")
	self:HookChatFrames()
	self:SetupOptions()
	
	-- Устанавливаем хук только после инициализации базы данных
	if Main and Main.db then
		local originalSetProfile = Main.db.SetProfile
		Main.db.SetProfile = function(self, ...)
			originalSetProfile(self, ...)
			AlphaProtocol.InterfaceModule:UpdateMainWindowRaidInfo()
		end
	end
end

-- Функция вызывается после загрузки всех переменных аддона
function OtherModule:OnVariablesLoaded()
	if Main.db.profile.showTimestamps == nil then
		Main.db.profile.showTimestamps = true
	end
	if Main.db.profile.showMainMenuButton == nil then
		Main.db.profile.showMainMenuButton = true
	end
	-- Инициализация настроек автосинхронизации
	if Main.db.profile.autoSyncLoot2 == nil then
		Main.db.profile.autoSyncLoot2 = true
	end
	if Main.db.profile.autoSyncNotes2 == nil then
		Main.db.profile.autoSyncNotes2 = true
	end
	if Main.db.profile.autoSyncTimestamps2 == nil then
		Main.db.profile.autoSyncTimestamps2 = true
	end
	if Main.db.profile.autoSendUpdates2 == nil then
		Main.db.profile.autoSendUpdates2 = true
	end
	if Main.db.profile.autoSendClearAdd2 == nil then
		Main.db.profile.autoSendClearAdd2 = true
	end
	-- Инициализация настроек сплит режима
end

-- Вспомогательная функция для определения канала отправки сообщений
function OtherModule:GetSendChannel()
	local pingChannel = AlphaProtocol:GetModule("StatusSyncModule"):GetPingResultChannel()
    
    if pingChannel then
        return pingChannel
    end

    -- Запускаем пинг, если он еще не запущен
    local StatusSyncModule = AlphaProtocol:GetModule("StatusSyncModule")
    if StatusSyncModule and not StatusSyncModule.isPingSending then
       -- print("|cFFFF9900[Синхронизация]|r Нет активного канала связи. Определяем оптимальный канал...")
        StatusSyncModule:SendPing()
    end
    
    -- Возвращаем nil, чтобы вызывающий код мог обработать ситуацию с задержкой отправки
    return nil
end


-- Перехват функции добавления сообщений в чат для всех окон чата
function OtherModule:HookChatFrames()
	for i = 1, NUM_CHAT_WINDOWS do
		local chatFrame = _G["ChatFrame" .. i]
		if chatFrame and not chatFrame.origAddMessage then
			chatFrame.origAddMessage = chatFrame.AddMessage
			chatFrame.AddMessage = function(frame, msg, ...)
				OtherModule:AddMessage(frame, msg, ...)
			end
		end
	end
end

-- Собственная функция добавления сообщений с временными метками
function OtherModule:AddMessage(frame, msg, ...)
	if Main.db.profile.showTimestamps then -- Проверяем, включены ли временные метки
		local timestamp = self:GetServerTimeFormatted()
		local formattedMsg = timestamp .. " " .. msg
		frame:origAddMessage(formattedMsg, ...)
	else
		frame:origAddMessage(msg, ...) -- Если метки выключены, отправляем сообщение без изменений
	end
end

-- Получение серверного времени в форматированном виде
function OtherModule:GetServerTimeFormatted()
	local hour, minute = GetGameTime()
	-- Установка фиксированного формата времени
	local formattedTime = string.format("[%02d:%02d]", hour, minute)
	return formattedTime
end

-- Настройка опций модуля
function OtherModule:SetupOptions()
	local options = {
		name = "Options",
		type = "group",
		args = {
			generalSettings = {
				type = "group",
				name = "General Settings",
				inline = true,
				order = 1,
				args = {
					showTimestamps = {
						type = "toggle",
						name = "Show Time in chat",
						desc = "Toggle the display of timestamps in chat messages.",
						get = function(info)
							return Main.db.profile.showTimestamps
						end,
						set = function(info, value)
							Main.db.profile.showTimestamps = value
						end,
						order = 1,
					},
					showMainMenuButton = {
						type = "toggle",
						name = "Show Main Menu Button",
						desc = "Toggle the display of the main menu button.",
						get = function(info)
							return Main.db.profile.showMainMenuButton
						end,
						set = function(info, value)
							Main.db.profile.showMainMenuButton = value
							AlphaProtocol.InterfaceModule:ToggleMainMenuButton()
						end,
						order = 2,
					},
					showMinimapIcon = {
						type = "toggle",
						name = "Show Minimap Icon",
						desc = "Toggle the display of the minimap icon.",
						get = function(info)
							return not Main.db.profile.minimap.hide
						end,
						set = function(info, value)
							Main.db.profile.minimap.hide = not value
							LDBIcon:Refresh("MinimapButton", Main.db.profile.minimap)
						end,
						order = 3,
					},
					allowMoveMainMenuButton = {
						type = "toggle",
						name = "Allow Moving Main Menu Button",
						desc = "Move main menu button",
						get = function(info)
							return Main.tempSettings.allowMoveMainMenuButton
						end,
						set = function(info, value)
							Main.tempSettings.allowMoveMainMenuButton = value
							AlphaProtocol.InterfaceModule:UpdateMenuButtonMovableState()
						end,
						order = 4,
					},
					spacer1 = {
						type = "description",
						name = "",
						order = 5,
					},
					raidManagerButton = {
						type = "execute",
						name = "Open Raid Manager",
						desc = "Opens the Raid Manager window",
						func = function()
							AlphaProtocol.RaidManagerModule:CreateRaidManagerWindow()
						end,
						order = 6,
					},
					taskSettingsButton = {
						type = "execute",
						name = "Open Task Settings",
						desc = "Opens the Task Settings window",
						func = function()
							AlphaProtocol.TaskConfigModule:CreateConfigWindow()
						end,
						order = 7,
					},
				},
			},
			textSettings = {
				type = "group",
				name = "Text Settings",
				inline = true,
				order = 2,
				args = {
					customText1 = {
						type = "input",
						name = "Custom Text 1",
						desc = "Enter your custom text 1.",
						get = function(info)
							return Main.db.profile.customText1
						end,
						set = function(info, value)
							Main.db.profile.customText1 = value
							AlphaProtocol.InterfaceModule:updateDropdownItems()
						end,
						order = 1,
					},
					customText2 = {
						type = "input",
						name = "Custom Text 2",
						desc = "Enter your custom text 2.",
						get = function(info)
							return Main.db.profile.customText2
						end,
						set = function(info, value)
							Main.db.profile.customText2 = value
							AlphaProtocol.InterfaceModule:updateDropdownItems()
						end,
						order = 2,
					},
					customText3 = {
						type = "input",
						name = "Custom Text 3",
						desc = "Enter your custom text 3.",
						get = function(info)
							return Main.db.profile.customText3
						end,
						set = function(info, value)
							Main.db.profile.customText3 = value
							AlphaProtocol.InterfaceModule:updateDropdownItems()
						end,
						order = 3,
					},
				},
			},
			autoSyncSettings = {
				type = "group",
				name = "AutoSync Settings",
				inline = true,
				order = 3,
				args = {
					autoSyncLoot2 = {
						type = "toggle",
						name = "Auto-sync loot",
						desc = "При записи лута в LootHistory, автоматически отправляет эту инорфмацию второму РЛу, на случай если он был не в радиусе сообщения в момент поднятия лута",
						get = function(info)
							return Main.db.profile.autoSyncLoot2
						end,
						set = function(info, value)
							Main.db.profile.autoSyncLoot2 = value
						end,
						order = 1,
					},
					autoSyncNotes2 = {
						type = "toggle",
						name = "Auto-sync notes",
						desc = "При изменении РЛ заметки, автоматически отправляет эту инорфмацию второму РЛу, если у второго РЛа эта опция выключена, то он просто примет эту заметку, если включена, то также отправит обратно свою и они соединятся через &",
						get = function(info)
							return Main.db.profile.autoSyncNotes2
						end,
						set = function(info, value)
							Main.db.profile.autoSyncNotes2 = value
						end,
						order = 2,
					},
					autoSyncTimestamps2 = {
						type = "toggle",
						name = "Auto-sync timestamps",
						desc = "При неудачном инвайте, автоматически отправляет эту инорфмацию второму РЛу, и она будет отображаться у него в PROBLEMS",
						get = function(info)
							return Main.db.profile.autoSyncTimestamps2
						end,
						set = function(info, value)
							Main.db.profile.autoSyncTimestamps2 = value
						end,
						order = 3,
					},
					autoSendUpdates2 = {
						type = "toggle",
						name = "Auto-send updates",
						desc = "Автоматически отправляет обновления второму РЛу",
						get = function(info)
							return Main.db.profile.autoSendUpdates2
						end,
						set = function(info, value)
							Main.db.profile.autoSendUpdates2 = value
						end,
						order = 4,
					},
					autoSendClearAdd2 = {
						type = "toggle",
						name = "Auto-send Clear/ADD",
						desc = "При нажатии на кнопку Clear/ADD, автоматически отправляет данные второму РЛу (будет принято в любом случае)",
						get = function(info)
							return Main.db.profile.autoSendClearAdd2
						end,
						set = function(info, value)
							Main.db.profile.autoSendClearAdd2 = value
						end,
						order = 5,
					},
				},
			},
		},
	}

	LibStub("AceConfig-3.0"):RegisterOptionsTable("FriendshipIsMagic", options)
	LibStub("AceConfigDialog-3.0"):AddToBlizOptions("FriendshipIsMagic", "FriendshipIsMagic")
end

-------------------------------------------------------------------------
-------------------- Синхронизация --------------------------------------
-------------------------------------------------------------------------
local aceComm = LibStub:GetLibrary("AceComm-3.0", true)
local AceSerializer = LibStub("AceSerializer-3.0", true)
local LibDeflate = LibStub:GetLibrary("LibDeflate", true)

-- Функция отправки сообщения
function OtherModule:SendCommMessage()
	--print("|cFF00FF00[Синхронизация]|r Начало процесса синхронизации...")
	if not aceComm or not AceSerializer or not LibDeflate then
		--print("|cFFFF0000[Синхронизация]|r Ошибка: не удалось загрузить необходимые библиотеки")
		return
	end

	if not Main.db.global.RaidDB or not Main.db.global.lastRaidID then
		--print("|cFFFF0000[Синхронизация]|r Ошибка: пустая база данных или не задан последний рейд")
		return
	end

	local senderGUID = UnitGUID("player")
	local lastRaidID = Main.db.global.lastRaidID
	--print("|cFF00FF00[Синхронизация]|r Подготовка данных рейда ID:", lastRaidID)

	local raidData = Main.db.global.RaidDB[lastRaidID]
	if raidData then
		--print("|cFF00FF00[Синхронизация]|r Найдены данные рейда, начинаю отправку...")
		self:sendData(raidData, lastRaidID, senderGUID)
	else
		--print("|cFFFF0000[Синхронизация]|r Ошибка: данные рейда отсутствуют для ID:", lastRaidID)
		return
	end
end

-- Функция генерации случайного 4-5-значного числа
function OtherModule:generateRandomSyncID()
	-- Генерируем случайное число от 1000 до 99999
	return math.random(10000, 99999)
end

function OtherModule:sendData(raidData, lastRaidID, senderGUID, Task)
	if not raidData then
		--print("|cFFFF0000[Синхронизация]|r Ошибка: Нет данных для отправки")
		return
	end

	--print("|cFF00FF00[Синхронизация]|r Генерация ID синхронизации...")
	raidData.syncID = self:generateRandomSyncID()
	--print("|cFF00FF00[Синхронизация]|r ID синхронизации:", raidData.syncID)

	raidData.RaidID = lastRaidID
	raidData.sender = senderGUID
	raidData.AddonTask = Task or "Direct"

	-- Проверка и очистка данных перед сериализацией
	self:sanitizeRaidData(raidData)

	--print("|cFF00FF00[Синхронизация]|r Сериализация данных...")
	local success, serializedData = pcall(function()
		return AceSerializer:Serialize(raidData)
	end)

	if not success or not serializedData then
		--print("|cFFFF0000[Синхронизация]|r Ошибка сериализации данных рейда:", tostring(serializedData))
		return
	end
	--print("|cFF00FF00[Синхронизация]|r Размер сериализованных данных: " .. #serializedData .. " байт")

	--print("|cFF00FF00[Синхронизация]|r Сжатие данных...")
	local compressedData = LibDeflate:CompressDeflate(serializedData, {
		level = 9,
	})
	if not compressedData then
		--print("|cFFFF0000[Синхронизация]|r Ошибка сжатия данных рейда")
		return
	end
	
	-- Проверка успешности сжатия
	local testDecompress = LibDeflate:DecompressDeflate(compressedData)
	if not testDecompress or testDecompress ~= serializedData then
		--print("|cFFFF0000[Синхронизация]|r Ошибка проверки сжатия данных")
		return
	end
	
	--print("|cFF00FF00[Синхронизация]|r Размер сжатых данных: " .. #compressedData .. " байт")

	--print("|cFF00FF00[Синхронизация]|r Кодирование данных...")
	local encodedData = LibDeflate:EncodeForWoWAddonChannel(compressedData)
	if not encodedData then
		--print("|cFFFF0000[Синхронизация]|r Ошибка кодирования данных рейда")
		return
	end
	--print("|cFF00FF00[Синхронизация]|r Размер кодированных данных: " .. #encodedData .. " байт")

	-- Проверка максимального размера сообщения
	local MAX_MESSAGE_SIZE = 4000 -- Максимальный размер сообщения в WoW
	if #encodedData > MAX_MESSAGE_SIZE * 50 then
		--print("|cFFFF0000[Синхронизация]|r Предупреждение: Данные слишком большие (" .. #encodedData .. " байт)")
	end

	-- Добавление контрольной суммы
	local checksum = self:calculateChecksum(encodedData)
	encodedData = "{START}" .. checksum .. "#" .. encodedData .. "{END}"
	
	-- Получаем канал отправки
	local channel = self:GetSendChannel()
	local target = nil
	
	-- Если нет доступного канала, сохраняем данные для отправки позже
	if not channel then
	--	print("|cFFFF9900[Синхронизация]|r Отправка отложена до получения канала связи")
		
		-- Создаем задачу, которая будет пытаться отправить данные, когда канал станет доступен
		local StatusSyncModule = AlphaProtocol:GetModule("StatusSyncModule")
		if StatusSyncModule then
			-- Добавляем таймер, который будет проверять наличие канала связи каждые 2 секунды
			C_Timer.After(2, function()
				-- Получаем канал отправки повторно
				local retryChannel = self:GetSendChannel()
				if retryChannel then
					-- Если канал доступен, отправляем данные
					self:sendData(raidData, lastRaidID, senderGUID, Task)
				else
					-- Если канал все еще недоступен, повторяем попытку позже
				--	print("|cFFFF9900[Синхронизация]|r Канал все еще недоступен, повторная попытка через 2 секунды")
					C_Timer.After(2, function()
						self:sendData(raidData, lastRaidID, senderGUID, Task)
					end)
				end
			end)
		end
		return
	end
	
	-- WHISPER канал требует получателя
	if channel == "WHISPER" and Main.db.profile.splitModeTarget and Main.db.profile.splitModeTarget ~= "" then
		target = Main.db.profile.splitModeTarget
	end
	
	-- Безопасная отправка сообщения с обработкой ошибок
	local function safeSendMessage(message, chunkNum, totalChunks)
		local success, err = pcall(function()
			C_ChatInfo.SendAddonMessage("FIMagicPrefix", message, channel, target)
		end)
		
		if not success then
			--print(string.format("|cFFFF0000[Синхронизация]|r Ошибка отправки части %d/%d: %s", chunkNum, totalChunks, tostring(err)))
			return false
		end
		return true
	end

	-- Используем фиксированный безопасный размер чанка
	local chunkSize = 200 -- Гарантированно работающий размер
	local totalChunks = math.ceil(#encodedData / chunkSize)
	--print("|cFF00FF00[Синхронизация]|r Отправка данных в " .. totalChunks .. " частях (размер чанка: " .. chunkSize .. " байт)...")

	-- Добавляем метаданные в каждый чанк для лучшего отслеживания
	for i = 1, #encodedData, chunkSize do
		local chunk = encodedData:sub(i, i + chunkSize - 1)
		local currentChunk = math.ceil(i/chunkSize)
		
		-- Добавляем метаданные к чанку
		local chunkWithMeta = string.format("CHUNK:%d:%d:", currentChunk, totalChunks) .. chunk
		
		--print("|cFF00FF00[Синхронизация]|r Отправка части " .. currentChunk .. "/" .. totalChunks .. " (размер: " .. #chunk .. " байт)")
		
		-- Увеличиваем задержку между чанками для надежности
		C_Timer.After((currentChunk - 1) * 0.2, function()
			if not safeSendMessage(chunkWithMeta, currentChunk, totalChunks) then
				--print("|cFFFF0000[Синхронизация]|r Ошибка при отправке чанка " .. currentChunk)
			end
		end)
	end
	--print("|cFF00FF00[Синхронизация]|r Отправка данных завершена")
end

-- Функция для очистки данных перед сериализацией
function OtherModule:sanitizeRaidData(data)
	if type(data) ~= "table" then return end
	
	for k, v in pairs(data) do
		if type(v) == "table" then
			self:sanitizeRaidData(v)
		elseif type(v) == "function" or type(v) == "userdata" then
			data[k] = nil
		end
	end
end

-- Функция для вычисления контрольной суммы
function OtherModule:calculateChecksum(data)
	local sum = 0
	for i = 1, #data do
		sum = sum + string.byte(data:sub(i,i))
	end
	return string.format("%x", sum % 65536)
end

-- Изменяем структуру буфера для поддержки нескольких отправителей и сообщений
local messageBuffers = {}
local expectedChunks = {}
local currentChunkCount = {}
local lastUpdateTime = {}
local lastMessageID

-- Функция очистки старых буферов
function OtherModule:CleanupOldBuffers()
	local currentTime = GetTime()
	for bufferKey, lastTime in pairs(lastUpdateTime) do
		if currentTime - lastTime > BUFFER_TIMEOUT and
		   (not currentChunkCount[bufferKey] or currentChunkCount[bufferKey] == expectedChunks[bufferKey]) then
		--	print("|cFFFF0000[LOOT Debug]|r Очистка устаревшего буфера:", bufferKey)
			messageBuffers[bufferKey] = nil
			currentChunkCount[bufferKey] = nil
			expectedChunks[bufferKey] = nil
			lastUpdateTime[bufferKey] = nil
		end
	end
end

-- Обновляем функцию HandleReceivedMessage для обработки сообщений о луте
function OtherModule:HandleReceivedMessage(prefix, message, channel, sender)
	if prefix ~= "FIMagicPrefix" then return end
	-- Очищаем старые буферы перед обработкой нового сообщения
	self:CleanupOldBuffers()

	-- Проверяем формат чанка
	local chunkNum, totalChunks, chunkData = message:match("^CHUNK:(%d+):(%d+):(.+)")
	if chunkNum and totalChunks and chunkData then
		chunkNum = tonumber(chunkNum)
		totalChunks = tonumber(totalChunks)
		
		-- Обновляем время последнего обновления для этого отправителя
		lastUpdateTime[sender] = GetTime()
		
		-- Инициализация буфера при получении первого чанка
		if chunkNum == 1 then
			messageBuffers[sender] = {}
			currentChunkCount[sender] = 0
			expectedChunks[sender] = totalChunks
		end

		-- Проверяем, существует ли буфер для этого отправителя
		if not messageBuffers[sender] then
			return
		end

		-- Сохраняем чанк в нужной позиции
		messageBuffers[sender][chunkNum] = chunkData
		currentChunkCount[sender] = (currentChunkCount[sender] or 0) + 1
		
		-- Проверяем, получены ли все чанки
		if currentChunkCount[sender] == totalChunks then
			local allChunksReceived = true
			local missingChunks = {}
			for i = 1, totalChunks do
				if not messageBuffers[sender][i] then
					allChunksReceived = false
					table.insert(missingChunks, i)
				end
			end

			if allChunksReceived then
				local fullMessage = table.concat(messageBuffers[sender])
				
				-- Очищаем буфер этого отправителя
				messageBuffers[sender] = nil
				currentChunkCount[sender] = nil
				expectedChunks[sender] = nil
				lastUpdateTime[sender] = nil
				
				-- Обрабатываем полное сообщение
				self:ProcessFullMessage(fullMessage, sender)
			end
		end
		return
	end

	if message:find("^UPDATE_CHUNK:") then
		--print("|cFF00FF00[UPDATE Debug]|r Получено сообщение UPDATE от", sender)
		-- Передаем все сообщение целиком, а не обрезанную версию
		self:HandleUpdateText(message, sender)
		return
	end

	if message:find("^TIMESTAMP:") then
		local encodedData = message:sub(11)
		self:HandleSingleTimestamp(encodedData, sender)
		return
	end

	if message:find("^LOOT_CHUNK:") then
		self:HandleSingleLoot(message, sender)
		return
	end

	if message:find("^NOTE:") then
		local encodedData = message:sub(6)
		self:HandleSingleNote(encodedData, sender)
		return
	end
end
C_ChatInfo.RegisterAddonMessagePrefix("FIMagicPrefix")
aceComm:RegisterComm("FIMagicPrefix", function(...)
	OtherModule:HandleReceivedMessage(...)
end)

-- Обновляем функцию слияния данных для обработки полных данных
function OtherModule:mergeRaidData(existingData, newData)
	if not newData or not newData.players then
		--print("|cFFFF0000[Синхронизация]|r Ошибка: Отсутствуют необходимые данные игроков")
		return
	end

	local updateUI = false

	-- Перебор всех игроков в новых данных
	for playerID, newPlayerData in pairs(newData.players) do
		local existingPlayerData = existingData.players[playerID]

		if existingPlayerData then
			-- Обновляем все данные игрока
			for key, value in pairs(newPlayerData) do
				if key == "rlinfo" then
					-- Не перезаписываем существующее значение пустым
					if value and value ~= "" then
						if existingPlayerData.rlinfo == "" then
							existingPlayerData.rlinfo = value
						else
							-- Если оба значения не пустые и разные, объединяем
							if value ~= existingPlayerData.rlinfo then
							existingPlayerData.rlinfo = existingPlayerData.rlinfo .. " #&# " .. value
							end
						end
					end
				elseif type(value) == "table" then
					-- Для массивов (временные метки, лут и т.д.)
					existingPlayerData[key] = existingPlayerData[key] or {}
					if key:find("Timestamps") or key == "bossKills" then
						-- Объединяем уникальные временные метки
						for _, timestamp in ipairs(value) do
							if not tContains(existingPlayerData[key], timestamp) then
								table.insert(existingPlayerData[key], timestamp)
							end
						end
					else
						-- Для других таблиц (например, playerLoot)
						for _, item in ipairs(value) do
							local isDuplicate = false
							for _, existingItem in ipairs(existingPlayerData[key]) do
								if self:areItemsEqual(item, existingItem) then
									isDuplicate = true
									break
								end
							end
							if not isDuplicate then
								table.insert(existingPlayerData[key], item)
							end
						end
					end
				else
					-- Для простых значений
					existingPlayerData[key] = value
				end
			end
			updateUI = true
		else
			-- Если игрока нет, добавляем все его данные
			existingData.players[playerID] = newPlayerData
			updateUI = true
		end
	end

	if updateUI then
		self:UpdatePlayersToInvite(existingData)
	end
end

-- Вспомогательная функция для сравнения предметов
function OtherModule:areItemsEqual(item1, item2)
	-- Функция для извлечения имени предмета из itemLink
	local function getItemNameFromLink(itemLink)
		-- Извлекаем текст между последними [] скобками
		local name = itemLink:match("%[(.-)%]")
		if not name then
			return itemLink
		end
		-- Убираем цветовые коды
		name = name:gsub("|c%x%x%x%x%x%x%x%x", ""):gsub("|r", "")
		return name
	end

	-- Сравниваем базовые имена предметов
	local itemNameMatch = getItemNameFromLink(item1.itemLink) == getItemNameFromLink(item2.itemLink)
	local raidIDMatch = item1.raidID == item2.raidID
	local playerNameMatch = item1.playerName == item2.playerName
	-- Допускаем небольшую разницу во времени (до 10 секунд)
	local timestampDiff = math.abs(item1.timestamp - item2.timestamp)
	local timestampNearMatch = timestampDiff <= 1
	
	local isEqual = itemNameMatch and raidIDMatch and playerNameMatch and timestampNearMatch
	
	if isEqual then
		--print("|cFFFF9900[Проверка дубликатов]|r Найден дубликат предмета: " .. item1.itemLink .. " для игрока " .. item1.playerName)
	end
	
	return isEqual
end

-- Обновляет интерфейс после синхронизации
function OtherModule:UpdatePlayersToInvite(raidData)
    if not AlphaProtocol.ManagmentModule.playersToInvite then
        AlphaProtocol.ManagmentModule.playersToInvite = {}
    end

    -- Очистка текущего списка
    wipe(AlphaProtocol.ManagmentModule.playersToInvite)

    -- Загрузка данных игроков
    if raidData and raidData.players then
        for playerID, details in pairs(raidData.players) do
            -- Преобразование данных игрока в формат, пригодный для интерфейса
            local playerInfo = {
                playerID = playerID,
                name = details.playerName,
                Task = details.playerTask,
                info = details.playerInfo,
                rlinfo = details.rlinfo,
                isVisible = details.isVisible or true,
                isHide = details.isHide, -- Загружаем статус скрытия
                declinedTimestamps = details.declinedTimestamps or {},
                noexistTimestamps = details.noexistTimestamps or {},
                busyTimestamps = details.busyTimestamps or {},
                leaveTimestamps = details.leaveTimestamps or {},
                expiredTimestamps = details.expiredTimestamps or {},
                bossKills = details.bossKills or {}
            }
            table.insert(AlphaProtocol.ManagmentModule.playersToInvite, playerInfo)
        end
    end

    -- Обновляем данные и интерфейс
    Main:LoadPlayersData()
    AlphaProtocol.ManagmentModule:UpdateMainWindow()
    AlphaProtocol.InterfaceModule:UpdateMainWindowRaidInfo()
end

-- Функция для отметки изменений в данных


-- Новая функция для обработки полного сообщения
function OtherModule:ProcessFullMessage(fullMessage, sender)
	local myIdentifier = UnitGUID("player")
	local messageSize = #fullMessage
	
	-- Проверка целостности сообщения
	if not fullMessage:find("{START}") or not fullMessage:find("{END}") then
		--print("|cFFFF0000[Синхронизация]|r Ошибка: Неполное сообщение от " .. sender)
		messageBuffers = {}
		return
	end

	--print("|cFF00FFFF[Синхронизация]|r Получено полное сообщение от " .. sender .. " (размер: " .. messageSize .. " байт)")
	
	-- Извлечение контрольной суммы и данных
	local checksum, encodedData = fullMessage:match("{START}([^#]+)#(.+){END}")
	if not checksum or not encodedData then
		--print("|cFFFF0000[Синхронизация]|r Ошибка: Неверный формат сообщения от " .. sender)
		messageBuffers = {}
		return
	end

	-- Проверка контрольной суммы
	local calculatedChecksum = self:calculateChecksum(encodedData)
	if calculatedChecksum ~= checksum then
		--print("|cFFFF0000[Синхронизация]|r Ошибка: Контрольная сумма не совпадает для сообщения от " .. sender)
		messageBuffers = {}
		return
	end

	messageBuffers = {}

	-- Декодирование данных
	local compressedData = LibDeflate:DecodeForWoWAddonChannel(encodedData)
	if not compressedData then
	--	print("|cFFFF0000[Синхронизация]|r Ошибка декодирования данных от " .. sender .. ". Возможно, данные повреждены при передаче.")
		return
	end
	
	-- Проверка размера сжатых данных
	if #compressedData < 10 then
	--	print("|cFFFF0000[Синхронизация]|r Ошибка: Слишком маленький размер сжатых данных от " .. sender)
		return
	end
	
	--print("|cFF00FFFF[Синхронизация]|r Успешное декодирование данных (размер: " .. #compressedData .. " байт)")

	-- Попытка распаковки с дополнительной проверкой
	local success, uncompressedString = pcall(function()
		return LibDeflate:DecompressDeflate(compressedData)
	end)

	if not success or not uncompressedString then
		--print("|cFFFF0000[Синхронизация]|r Ошибка распаковки данных от " .. sender .. ". Размер сжатых данных: " .. #compressedData)
		if not success then
			--print("|cFFFF0000[Синхронизация]|r Детали ошибки распаковки: " .. tostring(uncompressedString))
		end
		return
	end

	--print("|cFF00FFFF[Синхронизация]|r Успешная распаковка данных (размер: " .. #uncompressedString .. " байт)")

	local success, raidData = AceSerializer:Deserialize(uncompressedString)
	if success and raidData then
		--print("|cFF00FFFF[Синхронизация]|r Успешная десериализация данных")
		-- Проверка на дубликаты
		if raidData.syncID == lastMessageID then
			--print("|cFF00FFFF[Синхронизация]|r Пропуск дублирующего сообщения")
			return
		end
		lastMessageID = raidData.syncID

		-- Пропуск собственных сообщений
		if raidData.sender == myIdentifier then 
			--print("|cFF00FF00[Синхронизация Debug]|r Пропуск собственного сообщения")
			return 
		end

		-- Проверяем тип задачи
		if raidData.AddonTask == "ClearAdd" then
			--print("|cFF00FF00[CLEAR/ADD Debug]|r Обработка Clear/ADD сообщения")
			
			-- Проверяем, есть ли активный рейд, выключено
			--if Main.db.global.lastRaidID and Main.db.global.RaidDB[Main.db.global.lastRaidID] then
				--print("|cFFFF0000[CLEAR/ADD Debug]|r Отклонено: уже есть активный рейд")
			--	return
			--end

			if not raidData.clearAddText then
				--print("|cFFFF0000[CLEAR/ADD Debug]|r Ошибка: отсутствует текст Clear/ADD")
				return
			end

			--print("|cFF00FF00[CLEAR/ADD Debug]|r Применение Clear/ADD...")
			--print("  - Размер текста:", #raidData.clearAddText)
			
			-- Применяем Clear/ADD
			AlphaProtocol.ManagmentModule:AddRaidInformation(true, raidData.clearAddText)

			-- Обновляем данные и интерфейс
			Main:SavePlayersToRaidDB()
			Main:LoadPlayersData()
			AlphaProtocol.ManagmentModule:UpdateMainWindow()
			AlphaProtocol.InterfaceModule:UpdateMainWindowRaidInfo()

			-- Обновляем время последней синхронизации
			AlphaProtocol.InterfaceModule:UpdateLastSyncTime()
			
			-- Обновляем кэш статусов и инициируем синхронизацию
			if AlphaProtocol.StatusSyncModule then
				AlphaProtocol.StatusSyncModule:InitializeStatusCache()
				AlphaProtocol.StatusSyncModule:ForceSyncAllStatuses()
			end

			--print("|cFF00FF00[CLEAR/ADD Debug]|r Clear/ADD успешно применен")
			return
		end

		local receivedRaidID = raidData.RaidID
		--print("|cFF00FF00[Синхронизация Debug]|r Тип синхронизации: " .. (raidData.AddonTask or "Direct"))

		-- Обработка данных в зависимости от типа синхронизации
		if (Main.db.global.lastRaidID == nil or Main.db.global.lastRaidID == receivedRaidID) then
			-- Всегда используем слияние данных, независимо от типа синхронизации (Direct или Reverse)
				if not Main.db.global.RaidDB[receivedRaidID] then
				--print("|cFF00FF00[Синхронизация Debug]|r Создание нового рейда")
					Main.db.global.RaidDB[receivedRaidID] = raidData
					Main.db.global.lastRaidID = receivedRaidID
				else
				--print("|cFF00FF00[Синхронизация Debug]|r Обновление существующего рейда")
				--self:printRLInfo(Main.db.global.RaidDB[receivedRaidID], "До слияния")
				--self:printRLInfo(raidData, "Входящие данные")
					self:mergeRaidData(Main.db.global.RaidDB[receivedRaidID], raidData)
				--self:printRLInfo(Main.db.global.RaidDB[receivedRaidID], "После слияния")
				end

				-- Отправляем обратно полные данные только при первичной синхронизации
			if raidData.AddonTask ~= "Reverse" then
			--	print("|cFF00FF00[Синхронизация Debug]|r Подготовка ответных данных")
				local lastRaidID = Main.db.global.lastRaidID
				local responseData = self:deepCopy(Main.db.global.RaidDB[lastRaidID])
			--	self:printRLInfo(responseData, "Данные для отправки")
				local senderGUID = UnitGUID("player")
			--	print("|cFF00FF00[Синхронизация Debug]|r Отправка обратной синхронизации с GUID: " .. tostring(senderGUID))
				self:sendData(responseData, lastRaidID, senderGUID, "Reverse")
			end

			-- Обновляем время последней синхронизации
			AlphaProtocol.InterfaceModule:UpdateLastSyncTime()

			-- Обновляем интерфейс
			Main:LoadPlayersData()
			AlphaProtocol.ManagmentModule:UpdateMainWindow()
			AlphaProtocol.InterfaceModule:UpdateMainWindowRaidInfo()

			-- Обновляем время последней синхронизации
			AlphaProtocol.InterfaceModule:UpdateLastSyncTime()

			-- Принудительно инициализируем кэш статусов и запускаем синхронизацию
			if AlphaProtocol.StatusSyncModule then
				C_Timer.After(0.5, function()
					AlphaProtocol.StatusSyncModule:InitializeStatusCache()
					AlphaProtocol.StatusSyncModule:ForceSyncAllStatuses()
				end)
			end
		else
		--	print("|cFFFF0000[Синхронизация]|r Ошибка синхронизации: несовпадение ID рейдов")
		end
	else
	--	print("|cFFFF0000[Синхронизация]|r Ошибка десериализации данных от " .. sender)
	end
end

-- Функция глубокого копирования таблицы
function OtherModule:deepCopy(orig)
    local copy
    if type(orig) == "table" then
        copy = {}
        for orig_key, orig_value in pairs(orig) do
            copy[orig_key] = self:deepCopy(orig_value)
        end
    else
        copy = orig
    end
    return copy
end

-- Добавляем вспомогательную функцию для вывода rlinfo
function OtherModule:printRLInfo(raidData, label)
 --  --print("|cFF00FF00[" .. label .. "]|r")
    if raidData and raidData.players then
        for playerID, playerData in pairs(raidData.players) do
           --print("  " .. playerData.playerName .. ": rlinfo = '" .. (playerData.rlinfo or "") .. "'")
        end
    else
     --  --print("  Нет данных игроков")
    end
end

-- Функция для отправки одной заметки
function OtherModule:SendSingleNote(playerID, rlinfo, task)
    if not Main.db.global.lastRaidID then
        return
    end

    -- Проверяем настройку автосинхронизации заметок
    if not Main.db.profile.autoSyncNotes2 then
        return
    end

    local noteData = {
        raidID = Main.db.global.lastRaidID,
        playerID = playerID,
        rlinfo = rlinfo,
        sender = UnitGUID("player"),
        type = "SingleNote",
        AddonTask = task or "Direct"
    }

    local serializedData = AceSerializer:Serialize(noteData)
    local compressedData = LibDeflate:CompressDeflate(serializedData, { level = 9 })
    local encodedData = LibDeflate:EncodeForWoWAddonChannel(compressedData)
    
    if encodedData then
        local channel = self:GetSendChannel()
        local target = nil
        
        -- Если канал недоступен, отложить отправку
        if not channel then
          --  print("|cFFFF9900[Синхронизация заметок]|r Отправка отложена до получения канала связи")
            
            -- Создаем задачу для повторной попытки
            C_Timer.After(2, function()
                -- Получаем канал отправки повторно
                local retryChannel = self:GetSendChannel()
                if retryChannel then
                    -- Если канал доступен, отправляем данные
                    self:SendSingleNote(playerID, rlinfo, task)
                else
                    -- Если канал все еще недоступен, пробуем снова
                --    print("|cFFFF9900[Синхронизация заметок]|r Канал все еще недоступен, повторная попытка позже")
                    C_Timer.After(2, function()
                        self:SendSingleNote(playerID, rlinfo, task)
                    end)
                end
            end)
            return
        end
        
        -- WHISPER канал требует получателя
        if channel == "WHISPER" and Main.db.profile.splitModeTarget and Main.db.profile.splitModeTarget ~= "" then
            target = Main.db.profile.splitModeTarget
        end
        
        C_ChatInfo.SendAddonMessage("FIMagicPrefix", "NOTE:" .. encodedData, channel, target)
    end
end

-- Обработчик получения одной заметки
function OtherModule:HandleSingleNote(encodedData, sender)
    local compressedData = LibDeflate:DecodeForWoWAddonChannel(encodedData)
    if not compressedData then return end

    local uncompressedString = LibDeflate:DecompressDeflate(compressedData)
    if not uncompressedString then return end

    local success, noteData = AceSerializer:Deserialize(uncompressedString)
    if not success or not noteData then return end

    -- Проверяем, что это не наше собственное сообщение
    local myIdentifier = UnitGUID("player")
    if noteData.sender == myIdentifier then return end

    -- Проверяем совпадение ID рейда
    if noteData.raidID ~= Main.db.global.lastRaidID then return end

    -- Проверяем существование игрока
    local raidData = Main.db.global.RaidDB[noteData.raidID]
    if not raidData or not raidData.players[noteData.playerID] then return end

    local existingPlayer = raidData.players[noteData.playerID]
    local existingNote = existingPlayer.rlinfo or ""
    local newNote = noteData.rlinfo

    -- Если это обратная синхронизация, просто заменяем существующую заметку
    if noteData.AddonTask == "Reverse" then
        existingPlayer.rlinfo = newNote
        Main:LoadPlayersData()
        AlphaProtocol.ManagmentModule:UpdateMainWindow()
        return
    end

    -- Для прямой синхронизации проверяем на дубликаты
    local notes = { strsplit("#&#", existingNote) }
    local newNoteExists = false
    
    for _, note in ipairs(notes) do
        if string.trim(note) == string.trim(newNote) then
            newNoteExists = true
            break
        end
    end
    
    -- Обновляем только если заметка действительно новая
    if not newNoteExists then
        local finalNote = existingNote
        if existingNote == "" then
            finalNote = newNote
        else
            finalNote = existingNote .. " #&# " .. newNote
        end

        -- Обновляем локальные данные
        existingPlayer.rlinfo = finalNote
        Main:LoadPlayersData()
        AlphaProtocol.ManagmentModule:UpdateMainWindow()

        -- Отправляем обновленную заметку обратно только при прямой синхронизации
        C_Timer.After(0.5, function()
            self:SendSingleNote(noteData.playerID, finalNote, "Reverse")
        end)
    end
end

-- Функция для отправки одного предмета лута
function OtherModule:SendSingleLoot(playerID, lootData)
 --   print("|cFF00FF00[LOOT Debug]|r Начало отправки лута")
    if not Main.db.global.lastRaidID then
     --   print("|cFFFF0000[LOOT Debug]|r Ошибка: нет lastRaidID")
        return
    end

    -- Проверяем настройку автосинхронизации лута
    if not Main.db.profile.autoSyncLoot2 then
    ---    print("|cFFFF0000[LOOT Debug]|r Отправка лута отключена в настройках")
        return
    end

    -- Генерируем уникальный ID сообщения
    local messageID = tostring(time()) .. "_" .. math.random(1000, 9999)

  --  print("|cFF00FF00[LOOT Debug]|r Подготовка данных для отправки:")
   -- print("  - RaidID:", Main.db.global.lastRaidID)
   -- print("  - PlayerID:", playerID)
   -- print("  - Item:", lootData.itemLink)
   -- print("  - MessageID:", messageID)
   -- print("  - Sender:", UnitGUID("player"))

    local lootMessage = {
        raidID = Main.db.global.lastRaidID,
        playerID = playerID,
        lootData = lootData,
        sender = UnitGUID("player"),
        type = "SingleLoot",
        messageID = messageID
    }

  --  print("|cFF00FF00[LOOT Debug]|r Сериализация данных...")
    local serializedData = AceSerializer:Serialize(lootMessage)
   -- print("  - Размер сериализованных данных:", #serializedData)

   -- print("|cFF00FF00[LOOT Debug]|r Сжатие данных...")
    local compressedData = LibDeflate:CompressDeflate(serializedData, { level = 9 })
   -- print("  - Размер сжатых данных:", #compressedData)

    --print("|cFF00FF00[LOOT Debug]|r Кодирование данных...")
    local encodedData = LibDeflate:EncodeForWoWAddonChannel(compressedData)
   -- print("  - Размер закодированных данных:", #encodedData)
    
    if encodedData then
        -- Добавление контрольной суммы
        local checksum = self:calculateChecksum(encodedData)
        encodedData = "{START}" .. checksum .. "#" .. encodedData .. "{END}"
        
        -- Получаем канал отправки
        local channel = self:GetSendChannel()
        local target = nil
        
        -- Если нет доступного канала, сохраняем данные для отправки позже
        if not channel then
        --    print("|cFFFF9900[Синхронизация лута]|r Отправка отложена до получения канала связи")
            
            -- Создаем задачу для повторной попытки
            C_Timer.After(2, function()
                -- Получаем канал отправки повторно
                local retryChannel = self:GetSendChannel()
                if retryChannel then
                    -- Если канал доступен, отправляем данные
                    self:SendSingleLoot(playerID, lootData)
                else
                    -- Если канал все еще недоступен, пробуем снова
                --    print("|cFFFF9900[Синхронизация лута]|r Канал все еще недоступен, повторная попытка позже")
                    C_Timer.After(2, function()
                        self:SendSingleLoot(playerID, lootData)
                    end)
                end
            end)
            return
        end
        
        -- WHISPER канал требует получателя
        if channel == "WHISPER" and Main.db.profile.splitModeTarget and Main.db.profile.splitModeTarget ~= "" then
            target = Main.db.profile.splitModeTarget
        end
        
        -- Безопасная отправка сообщения с обработкой ошибок
        local function safeSendMessage(message, chunkNum, totalChunks)
            local success, err = pcall(function()
                C_ChatInfo.SendAddonMessage("FIMagicPrefix", message, channel, target)
            end)
            
            if not success then
               print(string.format("|cFFFF0000[LOOT Debug]|r Ошибка отправки части %d/%d: %s", chunkNum, totalChunks, tostring(err)))
                return false
            end
            return true
        end

        -- Используем фиксированный безопасный размер чанка
        local chunkSize = 200
        local totalChunks = math.ceil(#encodedData / chunkSize)
     --  print("|cFF00FF00[LOOT Debug]|r Отправка данных в " .. totalChunks .. " частях...")

        for i = 1, #encodedData, chunkSize do
            local chunk = encodedData:sub(i, i + chunkSize - 1)
            local currentChunk = math.ceil(i/chunkSize)
            
            -- Добавляем метаданные к чанку
            local chunkWithMeta = string.format("LOOT_CHUNK:%s:%d:%d:", messageID, currentChunk, totalChunks) .. chunk
            
          -- print("|cFF00FF00[LOOT Debug]|r Отправка части " .. currentChunk .. "/" .. totalChunks .. " (размер: " .. #chunk .. " байт)")
            
            -- Увеличиваем задержку между чанками для надежности
            C_Timer.After((currentChunk - 1) * 0.2, function()
                if not safeSendMessage(chunkWithMeta, currentChunk, totalChunks) then
              --      print("|cFFFF0000[LOOT Debug]|r Ошибка при отправке чанка " .. currentChunk)
                end
            end)
        end
       -- print("|cFF00FF00[LOOT Debug]|r Отправка данных завершена")
    end
end

-- Обработчик получения одного предмета лута
function OtherModule:HandleSingleLoot(message, sender)
   -- print("|cFF00FF00[LOOT Debug]|r Получено сообщение от", sender)
    
    -- Очищаем старые буферы перед обработкой нового сообщения
    self:CleanupOldBuffers()
    
    -- Проверяем формат чанка с messageID
    local messageID, chunkNum, totalChunks, chunkData = message:match("^LOOT_CHUNK:([^:]+):(%d+):(%d+):(.+)")
    if messageID and chunkNum and totalChunks and chunkData then
      --  print("|cFF00FF00[LOOT Debug]|r Обработка чанка", chunkNum, "из", totalChunks, "для сообщения", messageID)
        chunkNum = tonumber(chunkNum)
        totalChunks = tonumber(totalChunks)
        
        -- Создаем уникальный ключ для буфера
        local bufferKey = sender .. "_" .. messageID
        
        -- Обновляем время последнего обновления
        lastUpdateTime[bufferKey] = GetTime()
        
        -- Инициализация буфера при получении первого чанка
        if chunkNum == 1 then
         --   print("|cFF00FF00[LOOT Debug]|r Инициализация нового буфера для", sender, "messageID:", messageID)
            messageBuffers[bufferKey] = {}
            currentChunkCount[bufferKey] = 0
            expectedChunks[bufferKey] = totalChunks
        end

        -- Проверяем, существует ли буфер
        if not messageBuffers[bufferKey] then
          --  print("|cFFFF0000[LOOT Debug]|r Ошибка: буфер не существует для", sender, "messageID:", messageID)
            return
        end

        -- Сохраняем чанк
        messageBuffers[bufferKey][chunkNum] = chunkData
        currentChunkCount[bufferKey] = (currentChunkCount[bufferKey] or 0) + 1
       -- print("|cFF00FF00[LOOT Debug]|r Сохранен чанк", chunkNum, "всего получено:", currentChunkCount[bufferKey], "из", totalChunks)
        
        -- Проверяем, получены ли все чанки
        if currentChunkCount[bufferKey] == totalChunks then
            local allChunksReceived = true
            local missingChunks = {}
            for i = 1, totalChunks do
                if not messageBuffers[bufferKey][i] then
                    allChunksReceived = false
                    table.insert(missingChunks, i)
                end
            end

            if allChunksReceived then
               -- print("|cFF00FF00[LOOT Debug]|r Получены все чанки для сообщения", messageID)
                local fullMessage = table.concat(messageBuffers[bufferKey])
                
                -- Очищаем буфер
                messageBuffers[bufferKey] = nil
                currentChunkCount[bufferKey] = nil
                expectedChunks[bufferKey] = nil
                lastUpdateTime[bufferKey] = nil
                
                -- Обрабатываем полное сообщение
                self:ProcessFullLootMessage(fullMessage, sender)
            else
              --  print("|cFFFF0000[LOOT Debug]|r Отсутствуют чанки:", table.concat(missingChunks, ", "), "для сообщения", messageID)
            end
        end
        return
    end

    -- Обработка старого формата сообщений (для обратной совместимости)
    if message:sub(1, 5) == "LOOT:" then
      --  print("|cFF00FF00[LOOT Debug]|r Получено сообщение старого формата")
        local encodedData = message:sub(6)
        -- Обрабатываем сообщение как полное
        self:ProcessFullLootMessage("{START}0#" .. encodedData .. "{END}", sender)
    end
end

-- Новая функция для обработки полного сообщения лута
function OtherModule:ProcessFullLootMessage(fullMessage, sender)
  --  print("|cFF00FF00[LOOT Debug]|r Начало обработки полного сообщения от", sender)

    -- Проверка целостности сообщения
    if not fullMessage:find("{START}") or not fullMessage:find("{END}") then
      --  print("|cFFFF0000[LOOT Debug]|r Ошибка: Неполное сообщение")
        return
    end

    -- Извлечение контрольной суммы и данных
    local checksum, encodedData = fullMessage:match("{START}([^#]+)#(.+){END}")
    if not checksum or not encodedData then
     --   print("|cFFFF0000[LOOT Debug]|r Ошибка: Неверный формат сообщения")
        return
    end

    -- Проверка контрольной суммы
    local calculatedChecksum = self:calculateChecksum(encodedData)
    if calculatedChecksum ~= checksum then
      --  print("|cFFFF0000[LOOT Debug]|r Ошибка: Контрольная сумма не совпадает")
        return
    end

    --print("|cFF00FF00[LOOT Debug]|r Декодирование данных...")
    local compressedData = LibDeflate:DecodeForWoWAddonChannel(encodedData)
    if not compressedData then 
     --   print("|cFFFF0000[LOOT Debug]|r Ошибка декодирования данных")
        return 
    end

    --print("|cFF00FF00[LOOT Debug]|r Распаковка данных...")
    local uncompressedString = LibDeflate:DecompressDeflate(compressedData)
    if not uncompressedString then 
     --  print("|cFFFF0000[LOOT Debug]|r Ошибка распаковки данных")
        return 
    end

    --print("|cFF00FF00[LOOT Debug]|r Десериализация данных...")
    local success, lootMessage = AceSerializer:Deserialize(uncompressedString)
    if not success or not lootMessage then 
    --    print("|cFFFF0000[LOOT Debug]|r Ошибка десериализации данных")
        return 
    end

    -- Проверяем, что это не наше собственное сообщение
    local myIdentifier = UnitGUID("player")
    if lootMessage.sender == myIdentifier then 
    --   print("|cFF00FF00[LOOT Debug]|r Пропуск собственного сообщения")
        return 
    end

    -- Проверяем совпадение ID рейда
    if lootMessage.raidID ~= Main.db.global.lastRaidID then 
        --print("|cFFFF0000[LOOT Debug]|r Несовпадение ID рейда")
        return 
    end

    -- Проверяем существование игрока
    local raidData = Main.db.global.RaidDB[lootMessage.raidID]
    if not raidData or not raidData.players[lootMessage.playerID] then 
        --print("|cFFFF0000[LOOT Debug]|r Игрок не найден в базе данных")
        return 
    end

    local existingPlayer = raidData.players[lootMessage.playerID]
   -- print("|cFF00FF00[LOOT Debug]|r Обработка лута для игрока:", existingPlayer.playerName)
    
    -- Инициализируем массив лута, если его нет
    if not existingPlayer.playerLoot then
        existingPlayer.playerLoot = {}
    end

    -- Проверяем на дубликаты
    local isDuplicate = false
    for _, existingLoot in ipairs(existingPlayer.playerLoot) do
        if self:areItemsEqual(lootMessage.lootData, existingLoot) then
            isDuplicate = true
         --   print("|cFFFF0000[LOOT Debug]|r Найден дубликат предмета")
            break
        end
    end

    -- Если это не дубликат, добавляем предмет
    if not isDuplicate then
        --print("|cFF00FF00[LOOT Debug]|r Добавление нового предмета:", lootMessage.lootData.itemLink)
        table.insert(existingPlayer.playerLoot, lootMessage.lootData)
        Main:LoadPlayersData()
        AlphaProtocol.ManagmentModule:UpdateMainWindow()
    end
end

-- Функция для отправки timestamps
function OtherModule:SendSingleTimestamp(playerID, timestampType, timestamp)
    if not Main.db.global.lastRaidID then
        return
    end

    -- Проверяем настройку автосинхронизации timestamps
    if not Main.db.profile.autoSyncTimestamps2 then
        return
    end

    -- Получаем канал отправки
    local channel = self:GetSendChannel()

    -- Проверяем, является ли канал RAID
    if channel ~= "RAID" then
        -- print("|cFFFF9900[Синхронизация таймстампов]|r Канал не RAID ('" .. tostring(channel) .. "'), отправка отменена.")
        return
    end

    -- Если канал RAID, продолжаем отправку
    local timestampData = {
        raidID = Main.db.global.lastRaidID,
        playerID = playerID,
        timestampType = timestampType,
        timestamp = timestamp,
        sender = UnitGUID("player"),
        type = "SingleTimestamp",
        AddonTask = "Direct"
    }

    local serializedData = AceSerializer:Serialize(timestampData)
    local compressedData = LibDeflate:CompressDeflate(serializedData, { level = 9 })
    local encodedData = LibDeflate:EncodeForWoWAddonChannel(compressedData)
    
    if encodedData then
        local channel = self:GetSendChannel()
        local target = nil
        
        -- Если канал недоступен, отложить отправку
        if not channel then
         --   print("|cFFFF9900[Синхронизация таймстампов]|r Отправка отложена до получения канала связи")
            
            -- Создаем задачу для повторной попытки
            C_Timer.After(2, function()
                -- Получаем канал отправки повторно
                local retryChannel = self:GetSendChannel()
                if retryChannel then
                    -- Если канал доступен, отправляем данные
                    self:SendSingleTimestamp(playerID, timestampType, timestamp)
                else
                    -- Если канал все еще недоступен, пробуем снова
                --    print("|cFFFF9900[Синхронизация таймстампов]|r Канал все еще недоступен, повторная попытка позже")
                    C_Timer.After(2, function()
                        self:SendSingleTimestamp(playerID, timestampType, timestamp)
                    end)
                end
            end)
            return
        end
        
        -- WHISPER канал требует получателя
        if channel == "WHISPER" and Main.db.profile.splitModeTarget and Main.db.profile.splitModeTarget ~= "" then
            target = Main.db.profile.splitModeTarget
        end
        
        C_ChatInfo.SendAddonMessage("FIMagicPrefix", "TIMESTAMP:" .. encodedData, channel, target)
    end
end

-- Обработчик получения timestamp
function OtherModule:HandleSingleTimestamp(encodedData, sender)
   --print("Начало обработки timestamp")
    local compressedData = LibDeflate:DecodeForWoWAddonChannel(encodedData)
    if not compressedData then 
       --print("Ошибка декодирования данных")
        return 
    end

    local uncompressedString = LibDeflate:DecompressDeflate(compressedData)
    if not uncompressedString then 
       --print("Ошибка распаковки данных")
        return 
    end

    local success, timestampData = AceSerializer:Deserialize(uncompressedString)
    if not success or not timestampData then 
       --print("Ошибка десериализации данных")
        return 
    end


    -- Проверяем, что это не наше собственное сообщение
    local myIdentifier = UnitGUID("player")
    if timestampData.sender == myIdentifier then 
       --print("Пропуск собственного сообщения")
        return 
    end

    -- Проверяем совпадение ID рейда
    if timestampData.raidID ~= Main.db.global.lastRaidID then 
       --print("Несовпадение ID рейда")
        return 
    end

    -- Проверяем существование игрока
    local raidData = Main.db.global.RaidDB[timestampData.raidID]
    if not raidData or not raidData.players[timestampData.playerID] then 
       --print("Игрок не найден в базе данных")
        return 
    end

    local existingPlayer = raidData.players[timestampData.playerID]
   --print("Найден игрок:", existingPlayer.playerName)
    
    -- Инициализируем массив timestamps, если его нет
    local timestampArrayName = timestampData.timestampType .. "Timestamps"
   --print("Имя массива timestamps:", timestampArrayName)

    if not existingPlayer[timestampArrayName] then
       --print("Создание нового массива timestamps для игрока")
        existingPlayer[timestampArrayName] = {}
    end

    -- Проверяем на дубликаты
    local isDuplicate = false
    for _, existingTimestamp in ipairs(existingPlayer[timestampArrayName]) do
        if existingTimestamp == timestampData.timestamp then
            isDuplicate = true
           --print("Найден дубликат timestamp")
            break
        end
    end

    -- Если это не дубликат, добавляем timestamp
    if not isDuplicate then
       --print("Добавление нового timestamp")
        table.insert(existingPlayer[timestampArrayName], timestampData.timestamp)
        
        -- Сохраняем изменения
        Main:SavePlayersToRaidDB()
        Main:LoadPlayersData()
        AlphaProtocol.ManagmentModule:UpdateMainWindow()
        
       --print("Обновление завершено")
    end


end

-- Функция для отправки текста обновления
function OtherModule:SendUpdateText(updateText)
   --print("|cFF00FF00[UPDATE Debug]|r Начало отправки обновления")
    if not Main.db.global.lastRaidID then
       --print("|cFFFF0000[UPDATE Debug]|r Ошибка: нет lastRaidID")
        return
    end

    -- Проверяем настройку автоматической отправки обновлений
    if not Main.db.profile.autoSendUpdates2 then
       --print("|cFFFF0000[UPDATE Debug]|r Отправка обновлений отключена в настройках")
        return
    end

   --print("|cFF00FF00[UPDATE Debug]|r Подготовка данных для отправки:")
   --print("  - RaidID:", Main.db.global.lastRaidID)
   --print("  - Размер текста:", #updateText)
   --print("  - Sender:", UnitGUID("player"))

    local updateData = {
        raidID = Main.db.global.lastRaidID,
        updateText = updateText,
        sender = UnitGUID("player"),
        type = "UpdateText",
        AddonTask = "Direct"
    }

   --print("|cFF00FF00[UPDATE Debug]|r Сериализация данных...")
    local serializedData = AceSerializer:Serialize(updateData)
   --print("  - Размер сериализованных данных:", #serializedData)

   --print("|cFF00FF00[UPDATE Debug]|r Сжатие данных...")
    local compressedData = LibDeflate:CompressDeflate(serializedData, { level = 9 })
   --print("  - Размер сжатых данных:", #compressedData)

   --print("|cFF00FF00[UPDATE Debug]|r Кодирование данных...")
    local encodedData = LibDeflate:EncodeForWoWAddonChannel(compressedData)
   --print("  - Размер закодированных данных:", #encodedData)

    -- Добавление контрольной суммы
    local checksum = self:calculateChecksum(encodedData)
    encodedData = "{START}" .. checksum .. "#" .. encodedData .. "{END}"
    
    -- Получаем канал отправки
    local channel = self:GetSendChannel()
    local target = nil
    
    -- Если нет доступного канала, сохраняем данные для отправки позже
    if not channel then
      --  print("|cFFFF9900[Синхронизация обновлений]|r Отправка отложена до получения канала связи")
        
        -- Создаем задачу для повторной попытки
        C_Timer.After(2, function()
            -- Получаем канал отправки повторно
            local retryChannel = self:GetSendChannel()
            if retryChannel then
                -- Если канал доступен, отправляем данные
                self:SendUpdateText(updateText)
            else
                -- Если канал все еще недоступен, пробуем снова
            --    print("|cFFFF9900[Синхронизация обновлений]|r Канал все еще недоступен, повторная попытка позже")
                C_Timer.After(2, function()
                    self:SendUpdateText(updateText)
                end)
            end
        end)
        return
    end
    
    -- WHISPER канал требует получателя
    if channel == "WHISPER" and Main.db.profile.splitModeTarget and Main.db.profile.splitModeTarget ~= "" then
        target = Main.db.profile.splitModeTarget
    end
    
    -- Безопасная отправка сообщения с обработкой ошибок
    local function safeSendMessage(message, chunkNum, totalChunks)
        local success, err = pcall(function()
            C_ChatInfo.SendAddonMessage("FIMagicPrefix", message, channel, target)
        end)
        
        if not success then
           --print(string.format("|cFFFF0000[UPDATE Debug]|r Ошибка отправки части %d/%d: %s", chunkNum, totalChunks, tostring(err)))
            return false
        end
        return true
    end

    -- Используем фиксированный безопасный размер чанка
    local chunkSize = 200
    local totalChunks = math.ceil(#encodedData / chunkSize)
   --print("|cFF00FF00[UPDATE Debug]|r Отправка данных в " .. totalChunks .. " частях...")

    for i = 1, #encodedData, chunkSize do
        local chunk = encodedData:sub(i, i + chunkSize - 1)
        local currentChunk = math.ceil(i/chunkSize)
        
        -- Добавляем метаданные к чанку
        local chunkWithMeta = string.format("UPDATE_CHUNK:%d:%d:", currentChunk, totalChunks) .. chunk
        
       --print("|cFF00FF00[UPDATE Debug]|r Отправка части " .. currentChunk .. "/" .. totalChunks .. " (размер: " .. #chunk .. " байт)")
        
        -- Увеличиваем задержку между чанками для надежности
        C_Timer.After((currentChunk - 1) * 0.2, function()
            if not safeSendMessage(chunkWithMeta, currentChunk, totalChunks) then
               --print("|cFFFF0000[UPDATE Debug]|r Ошибка при отправке чанка " .. currentChunk)
            end
        end)
    end
   --print("|cFF00FF00[UPDATE Debug]|r Отправка данных завершена")
end

-- Обработчик получения текста обновления
function OtherModule:HandleUpdateText(encodedData, sender)
    --print("|cFF00FF00[UPDATE Debug]|r Обработка входящих данных от " .. sender)
    
    -- Очищаем старые буферы перед обработкой нового сообщения
    self:CleanupOldBuffers()
    
    -- Проверяем формат чанка
    local chunkNum, totalChunks, chunkData = encodedData:match("^UPDATE_CHUNK:(%d+):(%d+):(.+)")
    if chunkNum and totalChunks and chunkData then
       --print("|cFF00FF00[UPDATE Debug]|r Получен чанк " .. chunkNum .. "/" .. totalChunks)
        chunkNum = tonumber(chunkNum)
        totalChunks = tonumber(totalChunks)
        
        -- Обновляем время последнего обновления для этого отправителя
        lastUpdateTime[sender] = GetTime()
        
        -- Инициализация буфера при получении первого чанка
        if chunkNum == 1 then
           --print("|cFF00FF00[UPDATE Debug]|r Инициализация нового буфера для " .. sender)
            messageBuffers[sender] = {}
            currentChunkCount[sender] = 0
            expectedChunks[sender] = totalChunks
        end

        -- Проверяем, существует ли буфер для этого отправителя
        if not messageBuffers[sender] then
           --print("|cFFFF0000[UPDATE Debug]|r Ошибка: буфер не существует для " .. sender)
            return
        end

        -- Сохраняем чанк в нужной позиции
        messageBuffers[sender][chunkNum] = chunkData
        currentChunkCount[sender] = (currentChunkCount[sender] or 0) + 1
       --print("|cFF00FF00[UPDATE Debug]|r Сохранен чанк " .. chunkNum .. ", всего получено: " .. currentChunkCount[sender] .. "/" .. totalChunks)
        
        -- Проверяем, получены ли все чанки
        if currentChunkCount[sender] == totalChunks then
            local allChunksReceived = true
            local missingChunks = {}
            for i = 1, totalChunks do
                if not messageBuffers[sender][i] then
                    allChunksReceived = false
                    table.insert(missingChunks, i)
                end
            end

            if allChunksReceived then
               --print("|cFF00FF00[UPDATE Debug]|r Получены все чанки, собираем сообщение")
                local fullMessage = table.concat(messageBuffers[sender])
                
                -- Очищаем буфер этого отправителя
                messageBuffers[sender] = nil
                currentChunkCount[sender] = nil
                expectedChunks[sender] = nil
                lastUpdateTime[sender] = nil
                
                -- Обрабатываем полное сообщение
                self:ProcessUpdateMessage(fullMessage, sender)
            else
               --print("|cFFFF0000[UPDATE Debug]|r Отсутствуют чанки: " .. table.concat(missingChunks, ", "))
            end
        end
        return
    else
       --print("|cFFFF0000[UPDATE Debug]|r Неверный формат чанка: " .. encodedData:sub(1, 50) .. "...")
    end
end

-- Новая функция для обработки полного сообщения обновления
function OtherModule:ProcessUpdateMessage(fullMessage, sender)
   --print("|cFF00FF00[UPDATE Debug]|r Начало обработки полного сообщения от", sender)

    -- Проверка целостности сообщения
    if not fullMessage:find("{START}") or not fullMessage:find("{END}") then
       --print("|cFFFF0000[UPDATE Debug]|r Ошибка: Неполное сообщение")
        return
    end

    -- Извлечение контрольной суммы и данных
    local checksum, encodedData = fullMessage:match("{START}([^#]+)#(.+){END}")
    if not checksum or not encodedData then
       --print("|cFFFF0000[UPDATE Debug]|r Ошибка: Неверный формат сообщения")
        return
    end

    -- Проверка контрольной суммы
    local calculatedChecksum = self:calculateChecksum(encodedData)
    if calculatedChecksum ~= checksum then
       --print("|cFFFF0000[UPDATE Debug]|r Ошибка: Контрольная сумма не совпадает")
        return
    end

   --print("|cFF00FF00[UPDATE Debug]|r Декодирование данных...")
    local compressedData = LibDeflate:DecodeForWoWAddonChannel(encodedData)
    if not compressedData then 
       --print("|cFFFF0000[UPDATE Debug]|r Ошибка декодирования данных")
        return 
    end

   --print("|cFF00FF00[UPDATE Debug]|r Распаковка данных...")
    local uncompressedString = LibDeflate:DecompressDeflate(compressedData)
    if not uncompressedString then 
       --print("|cFFFF0000[UPDATE Debug]|r Ошибка распаковки данных")
        return 
    end

   --print("|cFF00FF00[UPDATE Debug]|r Десериализация данных...")
    local success, updateData = AceSerializer:Deserialize(uncompressedString)
    if not success or not updateData then 
       --print("|cFFFF0000[UPDATE Debug]|r Ошибка десериализации данных")
        return 
    end

    -- Проверяем, что это не наше собственное сообщение
    local myIdentifier = UnitGUID("player")
    if updateData.sender == myIdentifier then 
       --print("|cFFFF0000[UPDATE Debug]|r Пропуск собственного сообщения")
        return 
    end

    -- Проверяем совпадение ID рейда
    if updateData.raidID ~= Main.db.global.lastRaidID then 
       --print("|cFFFF0000[UPDATE Debug]|r Несовпадение ID рейда")
        return 
    end

   --print("|cFF00FF00[UPDATE Debug]|r Применение обновления...")
    AlphaProtocol.ManagmentModule:AddRaidInformation(false, updateData.updateText)

    -- Обновляем данные и интерфейс
    Main:SavePlayersToRaidDB()
    Main:LoadPlayersData()
    AlphaProtocol.ManagmentModule:UpdateMainWindow()
    AlphaProtocol.InterfaceModule:UpdateMainWindowRaidInfo()

    -- Обновляем время последней синхронизации
    AlphaProtocol.InterfaceModule:UpdateLastSyncTime()
    
    -- Проверяем изменения статусов после обновления данных
    if AlphaProtocol.StatusSyncModule then
        AlphaProtocol.StatusSyncModule:CheckStatusChanges()
    end

   --print("|cFF00FF00[UPDATE Debug]|r Обновление успешно применено")
end

-- Функция для отправки Clear/ADD текста
function OtherModule:SendClearAddText(clearAddText)
   --print("|cFF00FF00[CLEAR/ADD Debug]|r Начало отправки Clear/ADD")
    if not clearAddText then
       --print("|cFFFF0000[CLEAR/ADD Debug]|r Ошибка: пустой текст")
        return
    end

    -- Проверяем настройку автоматической отправки Clear/ADD
    if not Main.db.profile.autoSendClearAdd2 then
       --print("|cFFFF0000[CLEAR/ADD Debug]|r Отправка Clear/ADD отключена в настройках")
        return
    end

    -- Создаем структуру данных, аналогичную полной синхронизации
    local clearAddData = {
        RaidID = nil, -- Для Clear/ADD не используем существующий raidID
        clearAddText = clearAddText,
        sender = UnitGUID("player"),
        AddonTask = "ClearAdd",
        syncID = time() -- Используем время как уникальный ID синхронизации
    }

    -- Используем существующую функцию отправки данных
    self:sendData(clearAddData, nil, clearAddData.sender, "ClearAdd")
end

-- Обработчик получения Clear/ADD текста \ не используется
function OtherModule:HandleClearAddText(encodedData, sender)
   --print("|cFF00FF00[CLEAR/ADD Debug]|r Начало обработки полученного Clear/ADD от", sender)
 --   print("HandleClearAddText")
    -- Проверяем, есть ли активный рейд
    if Main.db.global.lastRaidID and Main.db.global.RaidDB[Main.db.global.lastRaidID] then
       --print("|cFFFF0000[CLEAR/ADD Debug]|r Отклонено: уже есть активный рейд")
        return
    end

    -- Используем существующие функции декодирования и десериализации
    local success, clearAddData = self:DecodeAndDeserialize(encodedData)
    if not success or not clearAddData then
       --print("|cFFFF0000[CLEAR/ADD Debug]|r Ошибка декодирования/десериализации данных")
        return
    end

   --print("|cFF00FF00[CLEAR/ADD Debug]|r Проверка данных:")
   --print("  - Тип задачи:", clearAddData.AddonTask)
   --print("  - Текст Clear/ADD:", clearAddData.clearAddText and #clearAddData.clearAddText or "отсутствует")

    -- Проверяем наличие необходимых данных
    if not clearAddData.clearAddText then
       --print("|cFFFF0000[CLEAR/ADD Debug]|r Ошибка: отсутствует текст Clear/ADD")
        return
    end

    -- Проверяем, что это не наше собственное сообщение
    local myIdentifier = UnitGUID("player")
   --print("|cFF00FF00[CLEAR/ADD Debug]|r Проверка отправителя:")
   --print("  - Мой GUID:", myIdentifier)
   --print("  - GUID отправителя:", clearAddData.sender)
    if clearAddData.sender == myIdentifier then 
       --print("|cFFFF0000[CLEAR/ADD Debug]|r Пропуск собственного сообщения")
        return 
    end

   --print("|cFF00FF00[CLEAR/ADD Debug]|r Применение Clear/ADD...")
   
    
    -- Применяем Clear/ADD
    AlphaProtocol.ManagmentModule:AddRaidInformation(true, clearAddData.clearAddText)

    -- Обновляем данные и интерфейс
    Main:SavePlayersToRaidDB()
    Main:LoadPlayersData()
    AlphaProtocol.ManagmentModule:UpdateMainWindow()
    AlphaProtocol.InterfaceModule:UpdateMainWindowRaidInfo()

    -- Обновляем время последней синхронизации
    -- Удаляем эту строку, так как функция не используется
    -- AlphaProtocol.InterfaceModule:UpdateLastSyncTime()

   --print("|cFF00FF00[CLEAR/ADD Debug]|r Clear/ADD успешно применен")
end

-- Вспомогательная функция для декодирования и десериализации
function OtherModule:DecodeAndDeserialize(encodedData)
   --print("|cFF00FF00[Debug]|r Декодирование данных...")
    local compressedData = LibDeflate:DecodeForWoWAddonChannel(encodedData)
    if not compressedData then 
       --print("|cFFFF0000[Debug]|r Ошибка декодирования данных")
        return false, nil
    end
   --print("  - Размер декодированных данных:", #compressedData)

   --print("|cFF00FF00[Debug]|r Распаковка данных...")
    local uncompressedString = LibDeflate:DecompressDeflate(compressedData)
    if not uncompressedString then 
       --print("|cFFFF0000[Debug]|r Ошибка распаковки данных")
        return false, nil
    end
   --print("  - Размер распакованных данных:", #uncompressedString)

   --print("|cFF00FF00[Debug]|r Десериализация данных...")
    local success, data = AceSerializer:Deserialize(uncompressedString)
    if not success or not data then 
       --print("|cFFFF0000[Debug]|r Ошибка десериализации данных")
        return false, nil
    end

    return true, data
end



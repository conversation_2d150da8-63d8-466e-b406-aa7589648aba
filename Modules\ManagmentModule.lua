-------------------- Универсальные функции ------------------------------
-------------------- Исправление ошибок в названии сервера --------------
-------------------- Получение и обработка информации -------------------
-------------------- Обновление интерфейса ------------------------------
-------------------- Перехватывает инвайты для таймера ------------------
-------------------- Всплывающее окно подсказка для информаций ----------
-------------------- Антиспам для сообщений -----------------------------
-------------------- Антиспам для инвайтов ------------------------------
-------------------- Счетчик присутствующих в рейде ---------------------
-------------------- Сортировка игроков ---------------------------------
-------------------- Шаблон для инвайтов --------------------------------
-------------------- Добавление информации лидером рейда ----------------
--[[
VA_666_6666|24.05.24|17:00|Alliance 7|#
Op_117_39596|PomPomBee|Aszune|VotI Normal / Heroic|[Один]|#
Op_117_39591|GoatsMilk|Doomhammer|VotI Normal / Heroic||#
Op_117_39593|PomPomReact|Doomhammer|VotI Normal / Heroic|Ачивка пощекотать пятки енотувайноту[Один]|#
]]
local AlphaProtocol = LibStub("AceAddon-3.0"):GetAddon("FriendshipIsMagic")
local ManagmentModule = AlphaProtocol:NewModule("ManagmentModule", "AceHook-3.0", "AceTimer-3.0")
local Main = AlphaProtocol:GetModule("Main")

-- Добавляем метод Print
function ManagmentModule:Print(message)
	if message then
		print("|cff33ff99FriendshipIsMagic|r: " .. message)
	end
end

function ManagmentModule:OnEnable()
	self:AntispamInitialize()
	self:AntispamMessageInitialize()
end

-- Функция для извлечения имени пилота из операторской информации
function ManagmentModule:ExtractPilotFromInfo(info)
	if not info then return nil, info end
	
	-- Ищем текст между первой парой маркеров ^^
	local start, finish = info:find("%^%^.-%^%^")
	if not start then return nil, info end
	
	-- Извлекаем имя пилота (без маркеров)
	local pilotName = info:sub(start + 2, finish - 2)
	
	-- Создаем очищенную строку, удаляя маркированный текст
	local cleanedInfo = info:sub(1, start - 1) .. info:sub(finish + 1)
	
	return pilotName, cleanedInfo
end

-- Функция для извлечения класса из операторской информации
function ManagmentModule:ExtractClassFromInfo(info)
	if not info then return nil, info end
	
	-- Ищем текст между / CLASS / (с пробелами)
	local start, finish = info:find("/ [^/]+ /")
	if not start then return nil, info end
	
	-- Извлекаем класс (без слешей и пробелов)
	local class = info:sub(start + 2, finish - 2):trim()
	
	-- Создаем очищенную строку, удаляя маркированный текст
	local cleanedInfo = info:sub(1, start - 1) .. info:sub(finish + 1)
	
	return class, cleanedInfo
end

-------------------------------------------------------------------------
-------------------- Универсальные функции ------------------------------
-------------------------------------------------------------------------
ManagmentModule.playersToInvite = {}
ManagmentModule.expectingEvents = false
ManagmentModule.strings = {
	WAITING_SPLIT = "Waiting",
	WAITING = "",
	DECLINED = "Declined",
	IN_RAID = "In Raid",
	OFFLINE_IN_RAID = "Offline in raid",
	NOEXIST = "Offline/Incorrect",
	BUSY = "Busy/In Group",
	LEAVE = "Left Group",
	AFK = "AFK",
	EXPIRED = "Expired",
	DONE = "DONE",
	ENEMY = "ENEMY",
	COMBAT = "Raid Combat",
	OTHER_RAID = "In Split",
}

-- Таблица для замены имен серверов
ManagmentModule.serverNameMapping = {
	["azuregos"] = "Азурегос",
	["boreantundra"] = "Борейскаятундра",
	["eversong"] = "Вечнаяпесня",
	["galakrond"] = "Галакронд",
	["goldrinn"] = "Голдринн",
	["gordunni"] = "Гордунни",
	["grom"] = "Гром",
	["fordragon"] = "Дракономор",
	["lichking"] = "Корольлич",
	["bootyBay"] = "Пиратскаябухта",
	["deepholm"] = "Подземье",
	["razuvius"] = "Разувий",
	["howlingfjord"] = "Ревущийфьорд",
	["soulflayer"] = "Свежевательдуш",
	["greymane"] = "Седогрив",
	["deathguard"] = "Стражсмерти",
	["thermaplugg"] = "Термоштепсель",
	["deathweaver"] = "Ткачсмерти",
	["blackscar"] = "Черныйшрам",
	["ashenvale"] = "Ясеневыйлес",
	------------- кастомные -------------
	["howling-fjord"] = "Ревущийфьорд",
}

-- Таблица цветов классов
ManagmentModule.classColors = {
	["MAGE"] = "|cFF69CCF0",
	["WARRIOR"] = "|cFFC79C6E",
	["DRUID"] = "|cFFFF7D0A",
	["PALADIN"] = "|cFFF58CBA",
	["MONK"] = "|cFF00FF96",
	["DH"] = "|cFFA330C9",
	["PRIEST"] = "|cFFFFFFFF",
	["EVOKER"] = "|cFF33937F",
	["ROGUE"] = "|cFFFFF569",
	["WARLOCK"] = "|cFF9482C9",
	["SHAMAN"] = "|cFF0070DE",
	["HUNTER"] = "|cFFABD473",
	["DK"] = "|cFFC41F3B",
}

-- Функция для получения цвета класса
function ManagmentModule:GetClassColor(class)
	if not class then return "" end
	-- Преобразуем сокращения в полные названия
	local classMapping = {
		["DH"] = "DH",
		["DK"] = "DK",
	}
	
	-- Получаем полное имя класса или используем исходное
	local fullClass = classMapping[class] or class:upper()
	return self.classColors[fullClass] or ""
end

-- Дополняет имя персонажа названием его сервера, если оно отсутствует.
function ManagmentModule:EnsureFullName(playerName)
	-- Проверяем, содержит ли имя персонажа дефис (разделитель имени и сервера).
	if not playerName:find("-") then
		-- Добавляем к имени персонажа дефис и название сервера текущего игрока.
		playerName = playerName .. "-" .. select(2, UnitFullName("player"))
	end
	-- Возвращаем полное имя персонажа, включая сервер.
	return playerName
end

-- Проверяет, находится ли указанный игрок в текущем рейде.
function ManagmentModule:IsPlayerInRaid(fullName)
	-- Перебираем всех участников рейда.
	for index = 1, MAX_RAID_MEMBERS do
		-- Получаем имя участника рейда.
		local raidMemberName = GetRaidRosterInfo(index)
		-- Если имя участника совпадает с искомым, возвращаем true.
		if fullName == raidMemberName then
			return true
		end
	end
	-- Если совпадений не найдено, возвращаем false.
	return false
end

-- Ищет и возвращает информацию об отслеживаемом персонаже по имени.
function ManagmentModule:GetTrackedPlayer(searchName)
	--print("DEBUG GetTrackedPlayer - Searching for:", searchName)
	-- Проверяем, есть ли список отслеживаемых игроков.
	if self.playersToInvite then
		-- Разбиваем искомое имя на имя и сервер
		local searchPlayerName, searchServer = strsplit("-", searchName)
		if searchServer then
			-- Проверяем маппинг серверов для поискового имени
			local originalServer = searchServer
			searchServer = ManagmentModule.serverNameMapping[searchServer:lower()] or searchServer
			searchName = searchPlayerName .. "-" .. searchServer
			--print("DEBUG GetTrackedPlayer - Server mapping:", originalServer, "->", searchServer)
		end
		
		-- Перебираем список отслеживаемых игроков.
		for index, playerInfo in pairs(self.playersToInvite) do
			-- Разбиваем имя отслеживаемого игрока на имя и сервер
			local playerName, playerServer = strsplit("-", playerInfo.name)
			if playerServer then
				-- Проверяем маппинг серверов для имени в списке
				local originalServer = playerServer
				playerServer = ManagmentModule.serverNameMapping[playerServer:lower()] or playerServer
				local fullName = playerName .. "-" .. playerServer
				--print("DEBUG GetTrackedPlayer - Comparing:", searchName:lower(), "with:", fullName:lower())
				
				-- Сравниваем имена в нижнем регистре
				if fullName:lower() == searchName:lower() then
				--	print("DEBUG GetTrackedPlayer - Found match!")
					-- Добавляем индекс игрока в его информацию.
					playerInfo.index = index
					-- Возвращаем информацию об игроке.
					return playerInfo
				end
			end
		end
	end
	--print("DEBUG GetTrackedPlayer - No match found")
	-- Возвращаем nil, если игрок не найден.
	return nil
end

-- Очистка RaidID при нажатии на Clear/ADD
function ManagmentModule:ClearRaidInformation(raidID)
	if not raidID then
		--print("Error: raidID is nil, cannot clear raid information.")
		return
	end

	if not Main.db or not Main.db.global.RaidDB then
		--print("Error: Database not properly initialized.")
		return
	end

	-- Проверяем, существует ли информация для данного RaidID
	if Main.db.global.RaidDB[raidID] then
		-- Очищаем информацию о рейде
		Main.db.global.RaidDB[raidID] = nil
		--print("Raid information for RaidID " .. tostring(raidID) .. " has been cleared.")
	else
		--print("No information found for RaidID " .. tostring(raidID) .. ".")
	end
end
-------------------------------------------------------------------------
-------------------- Исправление ошибок в названии сервера --------------
-------------------------------------------------------------------------
-- Находит наиболее похожее имя сервера на заданное inputServer среди списка серверов serverList, используя сходство триграмм.
function ManagmentModule:findClosestServerByTrigrams(inputServer, serverList)
	local inputTrigrams = self:generateTrigrams(inputServer)
	local maxSimilarity = 0
	local closestServer = inputServer -- Значение по умолчанию
	for _, server in ipairs(serverList) do
		local serverTrigrams = self:generateTrigrams(server)
		local similarity = self:trigramSimilarity(inputTrigrams, serverTrigrams)
		if similarity > maxSimilarity then
			maxSimilarity = similarity
			closestServer = server
		end
	end
	return closestServer
end

-- Функция генерирует и возвращает множество триграмм.
function ManagmentModule:generateTrigrams(name)
	local trigrams = {}
	local paddedName = "  " .. name:lower() .. "  "
	for i = 1, #paddedName - 2 do
		local trigram = paddedName:sub(i, i + 2)
		trigrams[trigram] = (trigrams[trigram] or 0) + 1
	end
	return trigrams
end

-- Вычисляет и возвращает меру сходства между двумя наборами триграмм.
function ManagmentModule:trigramSimilarity(trigrams1, trigrams2)
	local intersection = 0
	for trigram, count1 in pairs(trigrams1) do
		local count2 = trigrams2[trigram]
		if count2 then
			intersection = intersection + math.min(count1, count2)
		end
	end
	return intersection
end
-------------------------------------------------------------------------
-------------------- Получение и обработка информации -------------------
-------------------------------------------------------------------------
function ManagmentModule:AddRaidInformation(clearList, customList)
	--print("|cFF00FF00[AddRaidInformation Debug]|r Начало обработки")
	--print("  - clearList:", tostring(clearList))
	--print("  - customList:", type(customList) == "string" and #customList .. " bytes" or type(customList))

	local inviteMessage
	if customList then
		inviteMessage = customList
		--print("  - Используется переданный текст:", #inviteMessage, "bytes")
	else
		inviteMessage = AlphaProtocol.InterfaceModule.sidebarWindow.editBox:GetText()
		--print("  - Используется текст из editBox:", #inviteMessage, "bytes")
	end

	-- Получаем текст и разбиваем данные на строки.
	inviteMessage = inviteMessage:gsub("#\r?\n?", "##")
	local lines = {}
	for line in inviteMessage:gmatch("[^##]+") do
		table.insert(lines, line)
	end
	--print("  - Количество обработанных строк:", #lines)

	-- Проверяем, есть ли строки для обработки
	if #lines == 0 then
		--print("|cFFFF0000[AddRaidInformation Debug]|r Нет строк для обработки")
		return
	end

	-- Добавляем проверку на специфичные ключевые слова в начале первой строки
	local firstLine = lines[1]
	if firstLine:match("^ID:") or firstLine:match("^raidID:") then
		return
	end

	-- Обработка первой строки как информации о рейде при значении clearList - true
	local raidID, raidDate, raidTime, raidTeam
	if clearList then
		local doomsDay = time()
		-- Обработка первой строки как заголовка рейда
		local raidHeader = table.remove(lines, 1)
		raidID, raidDate, raidTime, raidTeam = raidHeader:match("(.-)%|+(.-)%|+(.-)%|+(.*)")
		raidTeam = string.trim(raidTeam or ""):gsub("|", "")

		if not raidID or not raidDate or not raidTime or not raidTeam then
			return
		end
        
        -- Модифицируем raidID, добавляя дату и время для большей уникальности
        raidID = raidID .. "-" .. raidDate .. "-" .. raidTime

		-- Всегда очищаем список приглашений при создании нового рейда
		wipe(self.playersToInvite)
		
		-- Вместо очистки данных рейда, сохраняем существующих игроков, если рейд уже существует
		if Main.db.global.RaidDB[raidID] then
			-- Копируем существующих игроков в список для отображения в интерфейсе
			local existingPlayers = Main.db.global.RaidDB[raidID].players
			for playerID, playerData in pairs(existingPlayers) do
				local fullName = playerData.playerName
				if playerData.serverName then
					fullName = fullName .. "-" .. playerData.serverName
				end
				
				table.insert(self.playersToInvite, {
					playerID = playerID,
					name = fullName,
					Task = playerData.playerTask or "",
					info = playerData.playerInfo or "",
					rlinfo = playerData.rlinfo or "",
					isVisible = playerData.isVisible or true,
					isHide = playerData.isHide,
					bossKills = playerData.bossKills,
					declinedTimestamps = playerData.declinedTimestamps or {},
					noexistTimestamps = playerData.noexistTimestamps or {},
					busyTimestamps = playerData.busyTimestamps or {},
					leaveTimestamps = playerData.leaveTimestamps or {},
					expiredTimestamps = playerData.expiredTimestamps or {},
					combatTimestamps = playerData.combatTimestamps or {},
					pilot = playerData.pilot or "",
				})
			end
			
			-- Обновляем метаданные рейда, но сохраняем список игроков
			Main.db.global.RaidDB[raidID].raidDate = raidDate
			Main.db.global.RaidDB[raidID].raidTime = raidTime
			Main.db.global.RaidDB[raidID].raidTeam = raidTeam
			Main.db.global.RaidDB[raidID].doomsDay = doomsDay
		else
			-- Создаем новую запись рейда, если рейд не существует
			Main.db.global.RaidDB[raidID] = {
				raidDate = raidDate,
				raidTime = raidTime,
				raidTeam = raidTeam,
				doomsDay = doomsDay,
				players = {},
			}
		end

		-- Устанавливаем lastRaidID
		Main.db.global.lastRaidID = raidID
	else
		raidID = Main.db.global.lastRaidID
	end

	local knownServers = {
		"Aegwynn",
		"AeriePeak",
		"Agamaggan",
		"Aggra(Português)",
		"Aggramar",
		"Ahn'Qiraj",
		"Al'Akir",
		"Alexstrasza",
		"Alleria",
		"Alonsus",
		"Aman'Thul",
		"Ambossar",
		"Anachronos",
		"Anetheron",
		"Antonidas",
		"Anub'arak",
		"Arakarahm",
		"Arathi",
		"Arathor",
		"Archimonde",
		"Area52",
		"ArgentDawn",
		"Arthas",
		"Arygos",
		"Aszune",
		"Auchindoun",
		"Azjolnerub",
		"Azshara",
		"Azuremyst",
		"Baelgun",
		"Balnazzar",
		"Blackhand",
		"Blackmoore",
		"Blackrock",
		"Blade'sEdge",
		"Bladefist",
		"Bloodfeather",
		"Bloodhoof",
		"Bloodscalp",
		"Blutkessel",
		"Boulderfist",
		"BronzeDragonflight",
		"Bronzebeard",
		"BurningBlade",
		"BurningLegion",
		"BurningSteppes",
		"C'Thun",
		"ChamberofAspects",
		"Chantséternels",
		"Cho'gall",
		"Chromaggus",
		"ConfrérieduThorium",
		"ConseildesOmbres",
		"Crushridge",
		"CultedelaRivenoire",
		"Сolinas-pardas",
		"Daggerspine",
		"Dalaran",
		"Dalvengyr",
		"DarkmoonFaire",
		"Darksorrow",
		"Darkspear",
		"DasKonsortium",
		"DasSyndikat",
		"Deathwing",
		"DefiasBrotherhood",
		"Dentarg",
		"DerMithrilorden",
		"DerRatvonDalaran",
		"DerabyssischeRat",
		"Destromath",
		"Dethecus",
		"DieAldor",
		"DieArguswacht",
		"DieNachtwache",
		"DieSilberneHand",
		"DieTodeskrallen",
		"DieewigeWacht",
		"Doomhammer",
		"Draenor",
		"Dragonblight",
		"Dragonmaw",
		"Drak'thul",
		"Drek'Thar",
		"DunModr",
		"DunMorogh",
		"Dunemaul",
		"Durotan",
		"EarthenRing",
		"Echsenkessel",
		"Eitrigg",
		"Eldre'Thalas",
		"Elune",
		"EmeraldDream",
		"Emeriss",
		"Eonar",
		"Eredar",
		"Executus",
		"Exodar",
		"FestungderStürme",
		"Forscherliga",
		"Frostmane",
		"Frostmourne",
		"Frostwhisper",
		"Frostwolf",
		"Garona",
		"Garrosh",
		"Genjuros",
		"Ghostlands",
		"Gilneas",
		"Gorgonnash",
		"GrimBatol",
		"Gul'dan",
		"Hakkar",
		"Haomarush",
		"Hellfire",
		"Hellscream",
		"Hyjal",
		"Illidan",
		"Jaedenar",
		"Kael'thas",
		"Karazhan",
		"Kargath",
		"Kazzak",
		"Kel'Thuzad",
		"Khadgar",
		"KhazModan",
		"Khaz'goroth",
		"Kil'jaeden",
		"Kilrogg",
		"KirinTor",
		"Kor'gall",
		"Krag'jin",
		"Krasus",
		"KulTiras",
		"KultderVerdammten",
		"LaCroisadeécarlate",
		"LaughingSkull",
		"LesClairvoyants",
		"LesSentinelles",
		"Lightbringer",
		"Lightning'sBlade",
		"Lordaeron",
		"LosErrantes",
		"Lothar",
		"Madmortem",
		"Magtheridon",
		"Mal'Ganis",
		"Malfurion",
		"Malorne",
		"Malygos",
		"Mannoroth",
		"MarécagedeZangar",
		"Mazrigos",
		"Medivh",
		"Minahonda",
		"Moonglade",
		"Mug'thol",
		"Nagrand",
		"Nathrezim",
		"Naxxramas",
		"Nazjatar",
		"Nefarian",
		"Nemesis",
		"Neptulon",
		"Ner'zhul",
		"Nera'thor",
		"Nethersturm",
		"Nordrassil",
		"Norgannon",
		"Nozdormu",
		"Onyxia",
		"Outland",
		"Perenolde",
		"Pozzodell'Eternità",
		"Proudmoore",
		"Quel'Thalas",
		"Ragnaros",
		"Rajaxx",
		"Rashgarroth",
		"Ravencrest",
		"Ravenholdt",
		"Rexxar",
		"Runetotem",
		"Sanguino",
		"Sargeras",
		"Saurfang",
		"ScarshieldLegion",
		"Sen'jin",
		"Shadowsong",
		"ShatteredHalls",
		"ShatteredHand",
		"Shattrath",
		"Shen'dralar",
		"Silvermoon",
		"Sinstralis",
		"Skullcrusher",
		"Spinebreaker",
		"Spirestone",
		"Sporeggar",
		"SteamwheedleCartel",
		"Stormrage",
		"Stormreaver",
		"Stormscale",
		"Sunstrider",
		"Suramar",
		"Sylvanas",
		"Taerar",
		"Talnivarr",
		"TarrenMill",
		"Teldrassil",
		"Templenoir",
		"Terenas",
		"Terokkar",
		"Terrordar",
		"TheMaelstrom",
		"TheSha'tar",
		"TheVentureCo",
		"Theradras",
		"Thrall",
		"Throk'Feroth",
		"Thunderhorn",
		"Tichondrius",
		"Tirion",
		"Todeswache",
		"Trollbane",
		"Turalyon",
		"Twilight'sHammer",
		"TwistingNether",
		"Tyrande",
		"Uldaman",
		"Ulduar",
		"Uldum",
		"Un'Goro",
		"Varimathras",
		"Vashj",
		"Vek'lor",
		"Vek'nilash",
		"Vol'jin",
		"Wildhammer",
		"Wrathbringer",
		"Xavius",
		"Ysera",
		"Ysondre",
		"Zenedar",
		"ZirkeldesCenarius",
		"Zul'jin",
		"Zuluhed",
		"Азурегос",
		"Борейскаятундра",
		"Вечнаяпесня",
		"Галакронд",
		"Голдринн",
		"Гордунни",
		"Гром",
		"Дракономор",
		"Корольлич",
		"Пиратскаябухта",
		"Подземье",
		"Разувий",
		"Ревущийфьорд",
		"Свежевательдуш",
		"Седогрив",
		"Стражсмерти",
		"Термоштепсель",
		"Ткачсмерти",
		"Черныйшрам",
		"Ясеневыйлес",
		"Azuregos",
		"BoreanTundra",
		"Eversong",
		"Galakrond",
		"Goldrinn",
		"Gordunni",
		"Grom",
		"Fordragon",
		"LichKing",
		"BootyBay",
		"Deepholm",
		"Razuvius",
		"HowlingFjord",
		"Soulflayer",
		"Greymane",
		"Deathguard",
		"Thermaplugg",
		"Deathweaver",
		"Blackscar",
		"Ashenvale",
		"Anasterian",
		"Benedictus"
	}

	local function customFirstToUpper(playerName)
		playerName = playerName or "Unknown"
		if playerName == "Unknown" then
			return playerName
		end

		local firstChar = string.utf8sub(playerName, 1, 1)
		local rest = string.utf8sub(playerName, 2)

		firstChar = string.utf8upper(firstChar) or firstChar

		--print("Первая найденная буква:", string.utf8upper(firstChar))

		return firstChar .. rest
	end

	-- Добавляем переменную-счётчик
	local lineNumber = 1

	-- Обрабатываем оставшиеся строки с информацией об игроках
	for _, line in ipairs(lines) do
		-- Проверяем, не является ли строка пустой или содержащей только пробелы
		if not line:match("^%s*$") then
			local playerID, playerName, serverName, playerTask, playerInfo =
				line:match("(.-)%|+(.-)%|+(.-)%|+(.-)%|+(.*)")

			----print("До: " .. playerName) -- Вывод до изменения
			playerName = customFirstToUpper(playerName)
			----print("После: " .. playerName) -- Вывод после изменения

			serverName = serverName or ""
			playerTask, playerInfo = playerTask or "", playerInfo or ""
			--print("DEBUG Server name before trigrams:", serverName)
			serverName = self:findClosestServerByTrigrams(serverName, knownServers)
			--print("DEBUG Server name after trigrams:", serverName)
			local originalServer = serverName
			serverName = ManagmentModule.serverNameMapping[serverName:lower()] or serverName
			--print("DEBUG Server name mapping:", originalServer, "->", serverName)

			--[[ Печать проверки для каждой строки
            print(string.format("ID: %s\n Имя: %s\n Сервер: %s\n Задача: %s\n Инфо: %s",
                                tostring(playerID), tostring(playerName),
                                tostring(serverName), tostring(playerTask),
                                tostring(playerInfo)))
            ]]

			-- Очистка и корректировка данных
			playerID = string.trim(playerID or "")
			playerName = string.trim(playerName or ""):gsub("|", "")
			serverName = string.trim(serverName or ""):gsub("|", "")
			playerTask = string.trim(playerTask or ""):gsub("|", "")
			playerInfo = string.trim(playerInfo or ""):gsub("|", "")

			-- Пропускаем обработку, если не хватает ключевых данных
			local foundPlayerIndex = nil
			for index, player in ipairs(self.playersToInvite) do
				if player.playerID == playerID then
					foundPlayerIndex = index
					break
				end
			end

			if foundPlayerIndex then
				-- Обновляем информацию о существующем игроке, сохраняя временные метки
				local existingPlayerInfo = self.playersToInvite[foundPlayerIndex]
				self.playersToInvite[foundPlayerIndex] = {
					playerID = playerID,
					name = playerName .. "-" .. serverName,
					Task = playerTask,
					info = playerInfo,
					rlinfo = self.playersToInvite[foundPlayerIndex].rlinfo or "",
					status = existingPlayerInfo.status or "", -- Сохраняем существующий статус или инициализируем
					pilot = existingPlayerInfo.pilot or "", -- Сохраняем информацию о пилоте
					isVisible = true,
					bossKills = existingPlayerInfo.bossKills or {},
					declinedTimestamps = existingPlayerInfo.declinedTimestamps or {},
					noexistTimestamps = existingPlayerInfo.noexistTimestamps or {},
					busyTimestamps = existingPlayerInfo.busyTimestamps or {},
					leaveTimestamps = existingPlayerInfo.leaveTimestamps or {},
					expiredTimestamps = existingPlayerInfo.expiredTimestamps or {},
				}
			else
				-- Добавляем нового игрока в список
				table.insert(self.playersToInvite, {
					playerID = playerID,
					name = playerName .. "-" .. serverName,
					Task = playerTask,
					info = playerInfo,
					rlinfo = "",
					status = "", -- Инициализируем status пустой строкой
					pilot = "", -- Инициализируем pilot пустой строкой
					isVisible = true,
					bossKills = {},
					declinedTimestamps = {},
					noexistTimestamps = {},
					busyTimestamps = {},
					leaveTimestamps = {},
					expiredTimestamps = {},
				})
			end

			-- Убираем вызов функции RaidList, так как она добавляет дублирующие данные
			-- Данные будут сохраняться через SavePlayersToRaidDB
		end
	end
	self:UpdateMainWindow()
	Main:SavePlayersToRaidDB()

end

-- Отладочная функци для печати объектов в консоль, красиво, в пальто белом.
function printTable(t)
	for key, value in pairs(t) do
		--print(tostring(key) .. ": " .. tostring(value))
	end
end
-------------------------------------------------------------------------
-------------------- Обновление интерфейса ------------------------------
-------------------------------------------------------------------------
function ManagmentModule:UpdateMainWindow()
	if not AlphaProtocol.InterfaceModule.mainWindow then
		AlphaProtocol.InterfaceModule:CreateMainWindow()
		AlphaProtocol.InterfaceModule:OptionalWindow()
		AlphaProtocol.InterfaceModule.optionalWindow:Hide()
		AlphaProtocol.InterfaceModule.mainWindow:Hide()
	end

	-- Временные переменные
	local playerData = {}
	local raidMembers = {}
	local hasActivePlayers = false
	local hasWaitingPlayers = false

	-- Получаем информацию о членах рейда
	for i = 1, MAX_RAID_MEMBERS do
		local playerName = GetRaidRosterInfo(i)
		if playerName then
			if not playerName:find("-") then
				playerName = playerName .. "-" .. select(2, UnitFullName("player"))
			end
			raidMembers[playerName:lower()] = true
		end
	end

	-- Обработка списка игроков для приглашения
	if self.playersToInvite then
		for _, player in pairs(self.playersToInvite) do
			-- Проверяем статус isHide перед добавлением в список отображения
			if player.isVisible and player.isHide ~= true then
				-- Инициализируем статус, если он не существует
				player.status = player.status or ""
				
				local isInRaid = raidMembers[player.name:lower()] ~= nil
				local statusIcon
				local timeLeftText

				if isInRaid then
					-- Игрок в рейде, проверяем онлайн статус
					local isOnline = false
					for i = 1, GetNumGroupMembers() do
						local name, _, _, _, _, _, _, online = GetRaidRosterInfo(i)
						if name then
							-- Обеспечиваем полное имя с сервером для сравнения
							if not name:find("-") then
								local serverName = GetRealmName():gsub(" ", "")
								name = name .. "-" .. serverName
							end

							if name:lower() == player.name:lower() then
								isOnline = online
								break
							end
						end
					end

					if isOnline then
						-- Игрок в рейде и онлайн
						player.status = ManagmentModule.strings.IN_RAID
						statusIcon = [=[Interface\RAIDFRAME\ReadyCheck-Ready]=]
					else
						-- Игрок в рейде но оффлайн
						player.status = ManagmentModule.strings.OFFLINE_IN_RAID
						statusIcon = [=[Interface\RAIDFRAME\ReadyCheck-Waiting]=] -- Используем иконку ожидания для оффлайн игроков
					end
					self:CancelTimer(player.timer)
					timeLeftText = "" -- Оставляем столбец Timer пустым
				elseif player.status == ManagmentModule.strings.IN_RAID or player.status == ManagmentModule.strings.OFFLINE_IN_RAID then
					-- Если игрок был в рейде, но сейчас его там нет - сбрасываем статус
					player.status = ""
					timeLeftText = ""
				elseif player.status == ManagmentModule.strings.OTHER_RAID then
					-- Игрок в другом рейде, используем специальную иконку
					statusIcon = [=[Interface\AddOns\FriendshipIsMagic\Assets\split]=]
					timeLeftText = ""
				else
					-- Остальная логика остается без изменений
					hasActivePlayers = true
					local timeLeft = self:TimeLeft(player.timer)
					if timeLeft > 0 then
						-- Ожидание игрока
						hasWaitingPlayers = true
						statusIcon = [=[Interface\RAIDFRAME\ReadyCheck-Waiting]=]
						timeLeftText = ""
						-- Устанавливаем статус с отображением таймера
						player.status = "|cFF00FF00" .. math.ceil(timeLeft) .. " сек|r"
					else
						-- Время ожидания истекло, иконка NotReady
						statusIcon = [=[Interface\RAIDFRAME\ReadyCheck-NotReady]=]
						timeLeftText = ""
						-- Проверяем, содержит ли статус строку "сек", что означает, что ранее был активен таймер
						-- Также проверяем, что статус не просто пустая строка (для новых игроков)
						if player.status and player.status ~= "" and (player.status == ManagmentModule.strings.WAITING or player.status:find("сек")) then
							player.status = ManagmentModule.strings.EXPIRED
							-- Получаем текущее серверное время и добавляем в expiredTimestamps игрока
							local currentTime = AlphaProtocol.EventsModule:GetServerTimeFormatted()
							table.insert(player.expiredTimestamps, currentTime)
						end
					end
				end

				-- Создание записи о игроке
				local messageTexture = not player.hasSentMessage and [=[Interface\Buttons\UI-GuildButton-MOTD-Up]=]
					or [=[Interface\Buttons\UI-GuildButton-MOTD-Disabled]=]
				
				-- Проверяем наличие маркеров пилота в информации
				if player.info and player.info:find("%^%^") then
					-- Извлекаем пилота из операторской информации только если есть маркеры
					local pilotName, cleanedInfo = self:ExtractPilotFromInfo(player.info)
					
					-- Обновляем информацию оператора без маркеров пилота и сохраняем пилота
					if pilotName then
						player.info = cleanedInfo
						player.pilot = pilotName
					end
				end
				
				-- Проверяем наличие информации о классе
				if player.info and player.info:find("/ [^/]+ /") then
					-- Извлекаем класс из операторской информации
					local class, cleanedInfo = self:ExtractClassFromInfo(player.info)
					
					-- Обновляем информацию оператора без маркеров класса и сохраняем класс
					if class then
						player.info = cleanedInfo
						player.class = class
					end
				end
				
				table.insert(playerData, {
					playerID = player.playerID,
					icon = statusIcon,
					name = player.name, -- Сохраняем оригинальное имя без цвета
					displayName = (self:GetClassColor(player.class) .. player.name .. "|r") or player.name, -- Добавляем отдельное поле для отображения с цветом
					status = player.status,
					timeLeft = player.pilot or "", -- Используем сохраненное значение pilot
					inviteTexture = [=[Interface\Buttons\UI-RefreshButton]=],
					messageTexture = messageTexture,
					copyStatusTexture = [=[Interface\FriendsFrame\InformationIcon]=],
					Task = player.Task,
					info = player.info,
					rlinfo = player.rlinfo,
				})
			end
		end
		-- Сортировка данных игроков по задаче, а затем по имени
		table.sort(playerData, function(a, b)
			if a.Task == b.Task then
				return a.name < b.name -- Сортировка по имени, если задачи одинаковы
			else
				return a.Task < b.Task -- Основная сортировка по задаче
			end
		end)

		-- Проставляем номера строк
		for i = 1, #playerData do
			playerData[i].countNumber = i -- Присваиваем номер строки каждому элементу
		end
	end
	AlphaProtocol.InterfaceModule.mainWindow.playerTable:SetData(playerData, true)

	-- Получение счётчика участников рейда
	local counterText = self:CountRaidParticipants()

	-- Обновление FontString с новым значением счётчика
	if AlphaProtocol.InterfaceModule.mainWindow.labelText then
		AlphaProtocol.InterfaceModule.mainWindow.labelText:SetText(" " .. counterText)
	end

	-- Запускаем или останавливаем таймер
	local hasActiveAndWaitingPlayers = hasActivePlayers and hasWaitingPlayers
	if hasActiveAndWaitingPlayers then
		if not self.progressTimer then
			-- Запуск таймера обновления окна прогресса
			self.progressTimer = self:ScheduleRepeatingTimer(function()
				self:UpdateMainWindow()
			end, 1)
		end
	else
		-- Остановка таймера
		self:CancelTimer(self.progressTimer)
		self.progressTimer = nil
	end
end
-------------------------------------------------------------------------
-------------------- Перехватывает инвайты для таймера ------------------
-------------------------------------------------------------------------
hooksecurefunc(C_PartyInfo, "InviteUnit", function(n)
	if AlphaProtocol.InterfaceModule.mainWindow and AlphaProtocol.InterfaceModule.mainWindow:IsVisible() then
		local fullName = ManagmentModule:EnsureFullName(n):lower()
		local playerExists = false
		local playerIndex = nil

		-- Проверяем, есть ли игрок в списке playersToInvite
		for index, player in ipairs(ManagmentModule.playersToInvite) do
			if player.name:lower() == fullName then
				playerExists = true
				playerIndex = index
				break
			end
		end

		-- Если игрок существует, обновляем его статус и таймер
		if playerExists then
			local playerData = ManagmentModule.playersToInvite[playerIndex]
			if ManagmentModule:TimeLeft(playerData.timer) == 0 then
				-- Обновляем таймер и статус
				local newTimer = ManagmentModule:ScheduleTimer(function() end, 60)
				playerData.timer = newTimer
				playerData.status = ManagmentModule.strings.WAITING
				playerData.sendingInvite = true -- Устанавливаем флаг при любой попытке приглашения
				ManagmentModule.playersToInvite[playerIndex] = playerData
				ManagmentModule:UpdateMainWindow()
			end
		else
			-- Если игрок не найден в списке, ничего не делаем
			-- Это предотвратит добавление новых игроков, которых нет в списке
		end
	end
end)
-------------------------------------------------------------------------
-------------------- Выгоняет игроков из группы -------------------------
-------------------------------------------------------------------------
function ManagmentModule:HandleKickClick(playerName)
	if IsInRaid(LE_PARTY_CATEGORY_HOME) then
		if UnitIsGroupLeader("player") or UnitIsGroupAssistant("player") then
			local raidIndex = UnitInRaid(playerName)
			if raidIndex then
				UninviteUnit(playerName)
				self:Print(playerName .. " has been kicked from the raid.")
			else
				local simplePlayerName = playerName:match("^[^-]+")
				if simplePlayerName then
					UninviteUnit(simplePlayerName)
					self:Print("Player " .. simplePlayerName .. " has been kicked from the raid.")
				else
					self:Print("Player " .. playerName .. " not found in the raid, and no alternative name could be derived.")
				end
			end
		else
			self:Print("You are not the leader or assistant and cannot kick players.")
		end
	elseif IsInGroup(LE_PARTY_CATEGORY_HOME) then
		if UnitIsGroupLeader("player") then
			if UnitInParty(playerName) then
				UninviteUnit(playerName)
				self:Print(playerName .. " has been kicked from the group.")
			else
				self:Print("Player " .. playerName .. " not found in the group.")
			end
		else
			self:Print("You are not the leader and cannot kick players.")
		end
	else
		self:Print("You are not in a group or raid.")
	end
end

-------------------------------------------------------------------------
-------------------- Всплывающее окно подсказка для информаций ----------
-------------------------------------------------------------------------
-- Показывает подсказку от Оператора
function ManagmentModule:CreateInfoTooltip(cell, data)
	if data and data.info then
		GameTooltip:SetOwner(cell, "ANCHOR_CURSOR")
		GameTooltip:SetText(data.info, 1, 1, 1, true)
		GameTooltip:Show()
	end
end

-- Показывает подсказку от Рейд лидера
function ManagmentModule:CreateInfoTooltipRL(cell, data)
	if data and data.rlinfo then
		GameTooltip:SetOwner(cell, "ANCHOR_CURSOR")
		GameTooltip:SetText(data.rlinfo, 1, 1, 1, true)
		GameTooltip:Show()
	end
end

-- Скрывает подсказку
function ManagmentModule:HideInfoTooltip()
	GameTooltip:Hide()
end

-- Показывает подсказку с полным текстом задачи
function ManagmentModule:CreateTaskTooltip(cell, data)
	if data and data.Task then
		GameTooltip:SetOwner(cell, "ANCHOR_CURSOR")
		GameTooltip:SetText(data.Task, 1, 1, 1, true)
		GameTooltip:Show()
	end
end

-- Показывает подсказку с полным именем пилота
function ManagmentModule:CreatePilotTooltip(cell, data)
	if data and data.timeLeft and data.timeLeft ~= "" then
		GameTooltip:SetOwner(cell, "ANCHOR_CURSOR")
		GameTooltip:SetText(data.timeLeft, 1, 1, 1, true)
		GameTooltip:Show()
	end
end
-------------------------------------------------------------------------
-------------------- Антиспам для сообщений -----------------------------
-------------------------------------------------------------------------
-- Инициализация таблицы для отслеживания времени последнего сообщения
function ManagmentModule:AntispamMessageInitialize()
	if not self.lastMessageTimes then
		self.lastMessageTimes = {}
	end
	self.messageCooldown = 5 -- Задаем интервал задержки в секундах для отправки сообщений
end

-- Функция для обработки нажатия кнопки отправки сообщения с антиспамом
function ManagmentModule:HandleSendMessageButtonClick(r)
	local predefinedMessageText = self.predefinedMessageText or ""
	local playerName = r.name

	if playerName then
		local playerFullName = self:EnsureFullName(playerName)
		
		if not self.lastMessageTimes[playerFullName] then
			self.lastMessageTimes[playerFullName] = 0
		end

		local currentTime = GetTime()
		local lastMessageTime = self.lastMessageTimes[playerFullName]

		if (currentTime - lastMessageTime) >= self.messageCooldown then
			SendChatMessage(predefinedMessageText, "WHISPER", nil, playerFullName)
			self.lastMessageTimes[playerFullName] = currentTime
			
		else
			local remainingTime = math.floor(self.messageCooldown - (currentTime - lastMessageTime))
			self:Print(
				"Подождите еще "
					.. remainingTime
					.. " секунд(ы), прежде чем отправлять сообщение "
					.. playerName
					.. " снова."
			)
		end
	end

	self.predefinedMessageText = predefinedMessageText
end
-------------------------------------------------------------------------
-------------------- Антиспам для инвайтов ------------------------------
-------------------------------------------------------------------------
-- Инициализация таблицы для отслеживания времени последнего приглашения
function ManagmentModule:AntispamInitialize()
	if not self.lastInviteTimes then
		self.lastInviteTimes = {}
	end
	self.inviteCooldown = 3 -- Задаем интервал задержки в секундах для всех
end

-- Функция антиспам для приглашения в рейд
function ManagmentModule:HandleInviteClick(playerName)
	-- Получаем информацию об игроке
	local playerInfo = self:GetTrackedPlayer(playerName)

	-- Проверяем, найден ли игрок и его статус
	if playerInfo then
		-- Проверка, не ожидает ли игрок приглашения от ДРУГОГО РЛа
		if playerInfo.status == self.strings.WAITING_SPLIT then
			self:Print(playerName .. " уже ожидает ответа на приглашение от другого лидера.")
			return
		end
	end

	-- Проверка антиспама
	if not self.lastInviteTimes[playerName] then
		self.lastInviteTimes[playerName] = 0
	end

	local currentTime = GetTime()
	local lastInviteTime = self.lastInviteTimes[playerName]

	if (currentTime - lastInviteTime) >= self.inviteCooldown then
		C_PartyInfo.InviteUnit(playerName)
		self.lastInviteTimes[playerName] = currentTime
	else
		local remainingTime = math.floor(self.inviteCooldown - (currentTime - lastInviteTime))
		self:Print(
			"Подождите еще "
				.. remainingTime
				.. " секунд(ы), прежде чем приглашать "
				.. playerName
				.. " снова."
		)
	end
end
-------------------------------------------------------------------------
-------------------- Счетчик присутствующих в рейде ---------------------
-------------------------------------------------------------------------
-- Счетчик присутствующих формата 10/10 (10 из 10)
function ManagmentModule:CountRaidParticipants()
	local totalInvited = #self.playersToInvite
	local inRaid = 0
	local raidMembers = {}

	for i = 1, MAX_RAID_MEMBERS do
		local name = GetRaidRosterInfo(i)
		if name then
			-- Проверяем, содержит ли имя символ "-"
			if not name:find("-") then
				-- Если символ "-" отсутствует, значит персонаж с нашего сервера, добавляем имя сервера
				local serverName = GetRealmName():gsub(" ", "")
				name = name .. "-" .. serverName
			end

			-- Добавляем полное имя участника рейда в таблицу для индексации
			raidMembers[name:lower()] = true
		end
	end

	-- Проверяем каждого игрока из списка на присутствие в рейде
	for _, playerInfo in ipairs(self.playersToInvite) do
		-- Формируем полное имя игрока, включая сервер, если он отсутствует
		local fullName = self:EnsureFullName(playerInfo.name)
		-- Сравниваем в нижнем регистре для избежания ошибок из-за регистра
		if raidMembers[fullName:lower()] then
			inRaid = inRaid + 1
		end
	end

	-- Получаем результат
	return string.format("%d/%d", inRaid, totalInvited)
end
-------------------------------------------------------------------------
-------------------- Сортировка игроков ---------------------------------
-------------------------------------------------------------------------
ManagmentModule.isDistributing = false

-- Основная функция распределения игроков по подгруппам
function ManagmentModule:DistributePlayers()
	if self.isDistributing then
		self:Print("Процесс распределения уже идет. Пожалуйста, подождите.")
		return
	end

	self.isDistributing = true

	if not UnitIsGroupLeader("player") and not UnitIsGroupAssistant("player") then
		self:Print("Недостаточно прав для распределения игроков по подгруппам.")
		self.isDistributing = false
		return
	end

	local subGroupCounts, playerIndexes = self:GatherGroupInfo()
	local invitedPlayers = self:MarkInvitedPlayers()

	self:ExchangePlayersBeforeDistribution(invitedPlayers, playerIndexes, subGroupCounts)

	-- Распределение приглашенных игроков
	for playerName, isInvited in pairs(invitedPlayers) do
		if isInvited then
			-- Игроки, которые должны быть в подгруппах с 5 по 8
			self:PlacePlayer(playerName, playerIndexes, subGroupCounts, 5, 8)
		else
			-- Игроки, которые должны быть в подгруппах с 1 по 4
			self:PlacePlayer(playerName, playerIndexes, subGroupCounts, 1, 4)
		end
	end

	self:CompressSubgroupsTowardsEnd(subGroupCounts, playerIndexes)

	self.isDistributing = false
end

-- Создаем таблицу где true - игроки из списка playersToInvite и false - рейдеры.
function ManagmentModule:MarkInvitedPlayers()
	local invitedPlayersMapping = {}
	-- Создание маппинга для playersToInvite
	for _, playerInfo in ipairs(self.playersToInvite) do
		local fullName = self:EnsureFullName(playerInfo.name):lower()
		invitedPlayersMapping[fullName] = true
	end

	local raidMembersMapping = {}
	-- Перебор всех игроков в рейде
	for i = 1, GetNumGroupMembers() do
		local name, _, _, _, _, _, _, _, _, _, _ = GetRaidRosterInfo(i)
		if name then
			local fullName = self:EnsureFullName(name):lower()
			-- Проверка на присутствие в playersToInvite и отметка игрока
			raidMembersMapping[fullName] = invitedPlayersMapping[fullName] == true
		end
	end

	return raidMembersMapping
end

-- Находим индекс всех игроков в рейде и подсчитывает количество игроков в подгруппе рейда.
function ManagmentModule:GatherGroupInfo()
	-- Таблица для подсчета количества членов в каждой подгруппе
	local subGroupCounts = { 0, 0, 0, 0, 0, 0, 0, 0 }
	-- Таблица для хранения индексов всех игроков в рейде
	local playerIndexes = {}

	-- Перебираем всех членов рейда
	for i = 1, GetNumGroupMembers() do
		local name, _, subGroup = GetRaidRosterInfo(i)
		if name then
			-- Приведение имени к нижнему регистру для унификации
			local fullName = self:EnsureFullName(name):lower()
			-- Увеличиваем счетчик для соответствующей подгруппы
			subGroupCounts[subGroup] = subGroupCounts[subGroup] + 1
			-- Сохраняем индекс игрока
			playerIndexes[fullName] = i
		end
	end

	-- Логируем итоговое количество членов в каждой подгруппе
	for subGroup = 1, #subGroupCounts do
	end

	-- Возвращаем результаты
	return subGroupCounts, playerIndexes
end

-- Размещает игрока в нужной группе
function ManagmentModule:PlacePlayer(playerName, playerIndexes, subGroupCounts, startGroup, endGroup)
	local currentIndex = playerIndexes[playerName]
	local _, _, currentGroup = GetRaidRosterInfo(currentIndex)

	-- Если игрок уже находится в подходящей подгруппе, не требуется никаких действий.
	if currentGroup >= startGroup and currentGroup <= endGroup then
		return
	end

	-- Ищем подгруппу для размещения, начиная с конца диапазона, чтобы минимизировать перемещения.
	for subGroup = endGroup, startGroup, -1 do
		if subGroupCounts[subGroup] < 5 then
			SetRaidSubgroup(currentIndex, subGroup)
			-- Обновляем счетчики подгрупп
			subGroupCounts[currentGroup] = subGroupCounts[currentGroup] - 1
			subGroupCounts[subGroup] = subGroupCounts[subGroup] + 1
			return
		end
	end
end

-- Функция для обмена персонажей между группами
function ManagmentModule:ExchangePlayersBeforeDistribution(invitedPlayers, playerIndexes, subGroupCounts)
	local potentialExchange = {}

	-- Собираем потенциальные пары для обмена
	for playerName, playerIndex in pairs(playerIndexes) do
		local currentGroup = select(3, GetRaidRosterInfo(playerIndex))
		local shouldBeInFirstHalf = not invitedPlayers[playerName]
		local isInvited = invitedPlayers[playerName]
		local desiredGroupRange = shouldBeInFirstHalf and { 1, 4 } or { 5, 8 }

		if not (currentGroup >= desiredGroupRange[1] and currentGroup <= desiredGroupRange[2]) then
			table.insert(potentialExchange, playerName)
		end
	end

	-- Пытаемся найти и выполнить обмен для каждого потенциального кандидата
	for _, playerName in ipairs(potentialExchange) do
		for _, targetName in ipairs(potentialExchange) do
			if playerName ~= targetName then
				local playerIndex = playerIndexes[playerName]
				local targetIndex = playerIndexes[targetName]
				local playerGroup = select(3, GetRaidRosterInfo(playerIndex))
				local targetGroup = select(3, GetRaidRosterInfo(targetIndex))

				-- Проверяем, подходят ли они для обмена
				local playerShouldBeInTargetGroup = invitedPlayers[playerName]
						and (targetGroup >= 5 and targetGroup <= 8)
					or (targetGroup >= 1 and targetGroup <= 4)
				local targetShouldBeInPlayerGroup = invitedPlayers[targetName]
						and (playerGroup >= 5 and playerGroup <= 8)
					or (playerGroup >= 1 and playerGroup <= 4)

				if playerShouldBeInTargetGroup and targetShouldBeInPlayerGroup then
					-- Выполняем обмен
					SetRaidSubgroup(playerIndex, targetGroup)
					SetRaidSubgroup(targetIndex, playerGroup)

					-- Обновляем счетчики подгрупп
					subGroupCounts[playerGroup], subGroupCounts[targetGroup] =
						subGroupCounts[targetGroup], subGroupCounts[playerGroup]

					-- После успешного обмена, выходим из внутреннего цикла
					break
				end
			end
		end
	end
end

function ManagmentModule:CompressSubgroupsTowardsEnd(subGroupCounts, playerIndexes)
	-- Перебор подгрупп с 8 по 5 для сжатия распределения
	for targetSubGroup = 8, 5, -1 do
		if subGroupCounts[targetSubGroup] < 5 then
			-- Ищем игроков для перемещения в эту подгруппу
			for sourceSubGroup = targetSubGroup - 1, 5, -1 do
				while subGroupCounts[sourceSubGroup] > 0 and subGroupCounts[targetSubGroup] < 5 do
					-- Перемещаем одного игрока
					self:MovePlayerToSubgroup(playerIndexes, subGroupCounts, sourceSubGroup, targetSubGroup)
				end
				if subGroupCounts[targetSubGroup] == 5 then
					break -- Целевая подгруппа заполнена
				end
			end
		end
	end
end

function ManagmentModule:MovePlayerToSubgroup(playerIndexes, subGroupCounts, sourceSubGroup, targetSubGroup)
	for playerName, index in pairs(playerIndexes) do
		local _, _, playerSubGroup = GetRaidRosterInfo(index)
		if playerSubGroup == sourceSubGroup then
			-- Найден игрок для перемещения
			SetRaidSubgroup(index, targetSubGroup)
			-- Обновляем счетчики
			subGroupCounts[sourceSubGroup] = subGroupCounts[sourceSubGroup] - 1
			subGroupCounts[targetSubGroup] = subGroupCounts[targetSubGroup] + 1
			break -- Перемещаем только одного игрока за раз
		end
	end
end
-------------------------------------------------------------------------
-------------------- Добавление информации лидером рейда ----------------
-------------------------------------------------------------------------
function ManagmentModule:HandleRowClick(cellFrame, rowData)
	local playerName = rowData and rowData.name
	if not playerName then
		return
	end

	local raidID = Main.db.global.lastRaidID
	if not raidID or not Main.db.global.RaidDB[raidID] then
		return
	end

	-- Если playerName доступно, ищем playerID
	local found = false
	for id, playerData in pairs(Main.db.global.RaidDB[raidID].players) do
		local fullPlayerName = playerData.playerName
		if playerData.serverName and playerData.serverName ~= "" then
			fullPlayerName = fullPlayerName .. "-" .. playerData.serverName
		end
		if fullPlayerName == playerName then
			found = true
			-- Найден соответствующий playerID
			ManagmentModule:SetupEditBox(id, playerName, raidID, cellFrame, rowData)
			break
		end
	end
end

function ManagmentModule:SetupEditBox(playerID, playerName, raidID, cellFrame, rowData)
	local editBox = AlphaProtocol.InterfaceModule.editBox or CreateFrame("EditBox", nil, UIParent, "InputBoxTemplate")
	AlphaProtocol.InterfaceModule.editBox = editBox

	-- Настройка и показ EditBox
	editBox:SetAutoFocus(true)
	editBox:SetFrameStrata("TOOLTIP")
	editBox:SetFrameLevel(100)
	editBox:SetWidth(cellFrame:GetWidth())
	editBox:SetHeight(cellFrame:GetHeight())
	editBox:SetPoint("TOPLEFT", cellFrame, "TOPLEFT")
	editBox:SetPoint("BOTTOMRIGHT", cellFrame, "BOTTOMRIGHT")
	editBox:SetText(rowData.rlinfo or "") -- Устанавливаем текущее значение информации об игроке
	editBox.raidID = raidID
	editBox.playerID = playerID
	editBox:Show()
	editBox:SetFocus()

	-- Обработчики событий EditBox
	editBox:SetScript("OnEnterPressed", function(self)
		local text = self:GetText()
		local updated = false

		-- Обновляем информацию в локальном списке
		for _, player in ipairs(AlphaProtocol.ManagmentModule.playersToInvite) do
			if player.playerID == self.playerID then
				player.rlinfo = text
				updated = true
				break
			end
		end

		-- Обновляем информацию в базе данных
		if Main.db.global.RaidDB[self.raidID] and Main.db.global.RaidDB[self.raidID].players[self.playerID] then
			Main.db.global.RaidDB[self.raidID].players[self.playerID].rlinfo = text
			updated = true
		end

		if updated then
			-- Отправляем обновленную заметку
			AlphaProtocol.OtherModule:SendSingleNote(self.playerID, text)
			ManagmentModule:UpdateMainWindow()
			Main:SavePlayersToRaidDB()
		end

		self:ClearFocus()
		self:Hide()
	end)

	editBox:SetScript("OnEscapePressed", function(self)
		self:ClearFocus()
		self:Hide()
	end)
end
-------------------------------------------------------------------------
-------------------- Фильтрация -----------------------------------------
-------------------------------------------------------------------------
function ManagmentModule:FilterTable(filterText)
	local visiblePlayers = {}
	local needUpdate = false -- Флаг, указывающий на необходимость обновления интерфейса

	local function trim(s)
		return s:match("^%s*(.-)%s*$")
	end

	local function extractBossInfo(info)
		local bosses = {}
		for boss in info:gmatch("%[(.-)%]") do
			table.insert(bosses, boss)
		end
		return bosses
	end

	local trimmedFilterText = trim(filterText)
	for _, player in ipairs(AlphaProtocol.ManagmentModule.playersToInvite) do
		local nameMatch = player.name
			and string.find(string.lower(player.name), string.lower(trimmedFilterText), 1, true)
		local bossInfoMatch = false
		if player.info then
			local bosses = extractBossInfo(player.info)
			for _, boss in ipairs(bosses) do
				if string.find(string.lower(boss), string.lower(trimmedFilterText), 1, true) then
					bossInfoMatch = true
					break
				end
			end
		end

		if (nameMatch or bossInfoMatch) and not player.isVisible then
			player.isVisible = true
			table.insert(visiblePlayers, player)
			needUpdate = true
		elseif not (nameMatch or bossInfoMatch) and player.isVisible then
			player.isVisible = false
			needUpdate = true
		end
	end

	if needUpdate then
		ManagmentModule:UpdateMainWindow()
	end
end

-------------------------------------------------------------------------
-------------------- Скрытие игроков ------------------------------------
-------------------------------------------------------------------------
-- Функция скрытия конкретного игрока
function ManagmentModule:HidePlayers(playerName)
	for _, player in ipairs(AlphaProtocol.ManagmentModule.playersToInvite) do
		if player.name == playerName then
			-- Проверяем, не находится ли игрок в рейде
			if player.status == ManagmentModule.strings.IN_RAID or player.status == ManagmentModule.strings.OFFLINE_IN_RAID then
				self:Print("Я вам запрещаю скрывать игрока " .. playerName .. ", так как он находится в рейде.")
				return
			end
			player.isHide = true
			break
		end
	end
	ManagmentModule:UpdateMainWindow()
	Main:SavePlayersToRaidDB() -- Сохраняем изменения в базу данных
	-- Обновляем таблицу скрытых игроков, если она открыта
	if AlphaProtocol.InterfaceModule.hiddenPlayersWindow then
		AlphaProtocol.InterfaceModule:UpdateHiddenPlayersTable()
	end
end

-- Функция для отображения всех игроков
function ManagmentModule:RevealAllPlayers()
	for _, player in ipairs(AlphaProtocol.ManagmentModule.playersToInvite) do
		player.isHide = false
	end
	ManagmentModule:UpdateMainWindow()
	Main:SavePlayersToRaidDB() -- Сохраняем изменения в базу данных
	-- Обновляем таблицу скрытых игроков, если она открыта
	if AlphaProtocol.InterfaceModule.hiddenPlayersWindow then
		AlphaProtocol.InterfaceModule:UpdateHiddenPlayersTable()
	end
end


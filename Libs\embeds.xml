<Ui xmlns="http://www.blizzard.com/wow/ui/"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.blizzard.com/wow/ui/
https://raw.githubusercontent.com/Gethe/wow-ui-source/live/Interface/FrameXML/UI_shared.xsd">
    <!--Be very careful about the order of these loads. LibStub must come first, followed by CallbackHandler and Ace Locale. Ace GUI bust come before AceDBOptions. -->

    <Script file="LibStub\LibStub.lua"/>
    <Script file="Libs\UTF8\utf8data.lua"/>
    <Script file="Libs\UTF8\utf8.lua"/>
    <Include file="CallbackHandler-1.0\CallbackHandler-1.0.xml"/>
    <Include file="StdUi\StdUi.xml"/>
    <Include file="AceLocale-3.0\AceLocale-3.0.xml"/>
    <Include file="AceGUI-3.0\AceGUI-3.0.xml"/>
    <Include file="AceAddon-3.0\AceAddon-3.0.xml"/>
    <Include file="AceBucket-3.0\AceBucket-3.0.xml"/>
    <Include file="AceComm-3.0\AceComm-3.0.xml"/>
    <Include file="LibCompress\lib.xml"/>
    <Include file="LibDeflate\LibDeflate.lua"/>
    <Include file="AceConfig-3.0\AceConfig-3.0.xml"/>
    <Include file="AceConsole-3.0\AceConsole-3.0.xml"/>
    <Include file="AceDB-3.0\AceDB-3.0.xml"/>
    <Include file="AceDBOptions-3.0\AceDBOptions-3.0.xml"/>
    <Include file="AceEvent-3.0\AceEvent-3.0.xml"/>
    <Include file="AceHook-3.0\AceHook-3.0.xml"/>
    <Include file="AceSerializer-3.0\AceSerializer-3.0.xml"/>
    <Include file="AceTab-3.0\AceTab-3.0.xml"/>
    <Include file="AceTimer-3.0\AceTimer-3.0.xml"/>
    <Include file="LibDataBroker-1.1\LibDataBroker-1.1.lua"/>
    <Include file="LibDBIcon-1.0\lib.xml"/>
    <Include file="LibEasyMenu\LibEasyMenu.lua"/>
</Ui>
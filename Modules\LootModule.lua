local AlphaProtocol = LibStub("AceAddon-3.0"):GetAddon("FriendshipIsMagic")
local LootModule = AlphaProtocol:NewModule("LootModule", "AceEvent-3.0")
local Main = AlphaProtocol:GetModule("Main")

function LootModule:OnEnable()
	-- Регистрация событий
	self:RegisterEvent("CHAT_MSG_LOOT", "LootInfo")

	--if not Main.db.global.LootHistoryDB then
	--Main.db.global.LootHistoryDB = {}
	--end
end

-- Функция для отладки параметров события
function LootModule:DebugEventArgs(...)
	local args = {...}
	----print("=== Параметры события CHAT_MSG_LOOT ===")
	for i, v in ipairs(args) do
		if v == "" then v = "<пусто>" end
		if v == nil then v = "<nil>" end
	--	--print(string.format("%d: %s (%s)", i, tostring(v), type(v)))
	end
	----print("=====================================")
end

-- Таблица для заполнения уникальных итемов.
local uniqueItems = {
	"Fyr'alath the Dreamrender",
}

function LootModule:NormalizePlayerName(name)
	-- Разделяем имя и сервер
	local playerName, serverName = name:match("^(.-)%-(.+)$")
	-- Если нет дефиса, используем всё имя целиком
	playerName = playerName or name
	-- Приводим к нижнему регистру и убираем пробелы
	return playerName:lower():gsub(" ", ""), serverName and serverName:lower():gsub(" ", "") or nil
end

--[[
Используем CHAT_MSG_LOOT для информации кто получил лут и какой.
	Стоит учитывать что всякий мусор тоже попадает в данную категорию.
	Если нужно именно с боссов, то нужно использовать ENCOUNTER_LOOT_RECEIVED, но он не учитывает возможные БОЕ из мобов.

Дальше можно попробовать вытащить через START_LOOT_ROLL и GetLootRollItemInfo нужную информацию по тем, кто хотел получить лут.
	Просто Имя игрока - Предмет - и что он нажал - Нужен, Не откажусь, Трансмог, Отказался/Истек таймер.
]]

function LootModule:LootInfo(event, ...)
	if event == "CHAT_MSG_LOOT" then
		-- Отладка параметров
		self:DebugEventArgs(...)

		local msg, _, _, _, looterName = ...
	----print("Получено сообщение о луте: " .. msg)
		
		-- Получаем itemLink из сообщения (пока оставляем, так как это самый надежный способ)
		local _, itemLink = msg:match("(.+) получает добычу: (.+)%.")
		if not itemLink then
			_, itemLink = msg:match("(.+) receives loot: (.+)%.")
		end

		if looterName and itemLink then
			-- Получаем информацию о предмете
			local _, _, itemRarity = GetItemInfo(itemLink)
			if itemRarity and (itemRarity == 3 or itemRarity == 4 or itemRarity == 5 or itemRarity == 6) then -- 3 = Редкие, 4 = Эпический, 5 = Легендарный
			--	--print("Игрок " .. looterName .. " получил " .. itemLink)
				self:SaveLootInfo(looterName, itemLink)
			else
				--print("Получен предмет низкого качества: " .. itemLink)
				return
			end
		else
			----print("Не удалось получить информацию о луте")
			return
		end
	end
end

function LootModule:GetLastKilledBoss(playerData)
	if not playerData.bossKills or #playerData.bossKills == 0 then
		return nil
	end
	return playerData.bossKills[#playerData.bossKills]
end

function LootModule:GetServerTimeOffset()
	-- Получаем текущее время сервера через игровое API
	local hours, minutes = GetGameTime()
	local serverTime = hours * 3600 + minutes * 60
	
	-- Получаем UTC время
	local utcTime = GetServerTime()
	local utcHours = tonumber(date("!%H", utcTime))
	local utcMinutes = tonumber(date("!%M", utcTime))
	local utcSeconds = utcHours * 3600 + utcMinutes * 60
	
	-- Вычисляем разницу
	local offset = serverTime - utcSeconds
	if offset > 43200 then -- более 12 часов
		offset = offset - 86400
	elseif offset < -43200 then
		offset = offset + 86400
	end
	
	return offset
end

-- Сохранение информации о добыче
function LootModule:SaveLootInfo(playerName, itemLink)
	local lastRaidID = Main.db.global.lastRaidID
	local lastRaid = Main.db.global.RaidDB[lastRaidID]

	if not lastRaid then
		return
	end

	local normalizedName, normalizedServer = self:NormalizePlayerName(playerName)

	for playerID, playerData in pairs(lastRaid.players) do
		local dbNormalizedName = self:NormalizePlayerName(playerData.playerName)

		if dbNormalizedName == normalizedName and 
		   (not normalizedServer or -- Если у искомого игрока нет сервера
		    not playerData.serverName or -- Или у игрока в БД нет сервера
		    self:NormalizePlayerName(playerData.serverName) == normalizedServer) then -- Или сервера совпадают
			
			if not playerData.playerLoot then
				playerData.playerLoot = {}
			end

			local lastBoss = self:GetLastKilledBoss(playerData)
			
			local lootData = {
				itemLink = itemLink,
				timestamp = GetServerTime(), -- Используем чистое UTC время для синхронизации
				raidID = lastRaidID,
				playerName = playerData.playerName,
				bossName = lastBoss -- Добавляем информацию о боссе
			}

			table.insert(playerData.playerLoot, lootData)
			--print("Успешно сохранен предмет " .. itemLink .. " для игрока " .. playerName)

			-- Отправляем информацию о луте другим игрокам
			AlphaProtocol.OtherModule:SendSingleLoot(playerID, lootData)

			-- Обновляем локальный интерфейс
			Main:LoadPlayersData()
			AlphaProtocol.ManagmentModule:UpdateMainWindow()
			return
		end
	end
end

--[[ NEW FUNCTION START ]]
-- Функция для получения полного текста всплывающей подсказки предмета
local hiddenTooltipName = "AlphaProtocolHiddenTooltip"
function LootModule:GetFullTooltipTextForItem(itemLink)
	if not itemLink then return "" end

	-- Создаем или используем существующий скрытый tooltip
	local tooltip = _G[hiddenTooltipName]
	if not tooltip then
		tooltip = CreateFrame("GameTooltip", hiddenTooltipName, UIParent, "GameTooltipTemplate")
		tooltip:SetOwner(UIParent, "ANCHOR_NONE")
		_G[hiddenTooltipName] = tooltip
	end

	tooltip:ClearLines() -- Очищаем перед использованием
	tooltip:SetHyperlink(itemLink)

	local fullText = ""
	local numLines = tooltip:NumLines()

	-- Собираем текст со всех строк (левая и правая части)
	for i = 1, numLines do
		local leftTextWidget = _G[hiddenTooltipName .. "TextLeft" .. i]
		local rightTextWidget = _G[hiddenTooltipName .. "TextRight" .. i]
		if leftTextWidget then
			local text = leftTextWidget:GetText()
			if text and text ~= "" then
				fullText = fullText .. " " .. text
			end
		end
		if rightTextWidget then
			local text = rightTextWidget:GetText()
			if text and text ~= "" then
				fullText = fullText .. " " .. text
			end
		end
	end

	tooltip:ClearLines() -- Очищаем после использования
	-- tooltip:Hide() -- Скрываем на всякий случай, хотя он и так не должен быть видим

	return fullText:lower() -- Возвращаем текст в нижнем регистре для удобства сравнения
end
--[[ NEW FUNCTION END ]]

-------------------------------------------------------------
------------------------- Интерфейс -------------------------
-------------------------------------------------------------

-- Функция для обновления данных лута (перенесена сюда и сделана методом модуля)
function LootModule:RefreshLootData()
	if not Main.db.global.lastRaidID then return end
	
	local raidData = Main.db.global.RaidDB[Main.db.global.lastRaidID]
	if not raidData then return end

	-- Обновляем данные из RaidDB
	for _, playerData in pairs(raidData.players) do
		if playerData.playerLoot then
			for _, lootData in ipairs(playerData.playerLoot) do
				if not lootData.raidID then
					lootData.raidID = Main.db.global.lastRaidID
				end
			end
		end
	end
end

-- Изменено: теперь функция создает окно, если его нет, и возвращает его.
-- Показ/скрытие и обновление данных управляются из InterfaceModule.
function LootModule:GetOrCreateLootWindow()
	if self.lootFrame then
		return self.lootFrame -- Возвращаем существующее окно
	end

	-- --- Создание окна, если оно не существует --- 
	local StdUi = LibStub("StdUi")
	local lootFrame = StdUi:Window(UIParent, 800, 600, "Loot History")
	lootFrame:SetPoint("CENTER")
	lootFrame:Hide() -- Окно создается скрытым
	
	-- Добавляем возможность закрытия окна по ESC (только при создании)
	AlphaProtocol.InterfaceModule:EnableEscapeKey(lootFrame)
	-- Обработчик обновления уровня фрейма (только при создании)
	lootFrame:SetScript("OnMouseDown", function(frame) -- Используем frame вместо self
		frame:SetFrameLevel(AlphaProtocol.InterfaceModule:GetNextFrameLevel())
	end)

	-- Создаем панель фильтров
	local filterPanel = StdUi:Panel(lootFrame, lootFrame:GetWidth() - 20, 30)
	filterPanel:SetPoint("TOPLEFT", lootFrame, "TOPLEFT", 10, -30)
	filterPanel:SetPoint("TOPRIGHT", lootFrame, "TOPRIGHT", -10, -30)

	-- Добавляем метку для выпадающего списка
	local bossLabel = StdUi:Label(filterPanel, "Босс:", 12)
	bossLabel:SetPoint("LEFT", filterPanel, "LEFT", 5, 0)

	-- Создаем поле поиска
	local searchBox = StdUi:EditBox(filterPanel, 200, 20, "")
	searchBox:SetPoint("LEFT", filterPanel, "CENTER", 10, 0)
	lootFrame.searchBox = searchBox -- Сохраняем ссылку на поле поиска

	-- Добавляем кнопку черного списка
	local blacklistButton = StdUi:Button(filterPanel, 100, 20, "Черный список")
	blacklistButton:SetPoint("LEFT", searchBox, "RIGHT", 10, 0)

	-- Обработчик нажатия на кнопку черного списка
	blacklistButton:SetScript("OnClick", function()
		self:CreateBlacklistWindow() -- 'self' здесь все еще LootModule
	end)

	-- Добавляем иконку поиска
	local searchIcon = filterPanel:CreateTexture(nil, "ARTWORK")
	searchIcon:SetTexture("Interface\\Common\\UI-Searchbox-Icon")
	searchIcon:SetSize(14, 14)
	searchIcon:SetPoint("RIGHT", searchBox, "RIGHT", -5, 0)
	searchIcon:SetVertexColor(0.6, 0.6, 0.6, 0.8)

	-- Добавляем placeholder текст
	searchBox.placeholder = searchBox:CreateFontString(nil, "OVERLAY", "GameFontNormal")
	searchBox.placeholder:SetPoint("LEFT", searchBox, "LEFT", 5, 0)
	searchBox.placeholder:SetText("Поиск...")
	searchBox.placeholder:SetTextColor(0.5, 0.5, 0.5, 1)

	-- Настраиваем внешний вид поля поиска
	searchBox:SetTextInsets(5, 20, 0, 0)

	-- Функция для получения списка боссов (внутренняя для окна)
	function lootFrame:GetBossList()
		local bosses = {}
		local bossesSet = {}
		local lastRaidID = Main.db.global.lastRaidID
		local lastRaid = Main.db.global.RaidDB[lastRaidID]

		-- Добавляем опцию "Весь лут"
		table.insert(bosses, {text = "All loot", value = "all"})

		if lastRaid and lastRaid.players then
			for _, playerData in pairs(lastRaid.players) do
				if playerData.playerLoot then
					for _, lootData in ipairs(playerData.playerLoot) do
						if lootData.raidID == lastRaidID then
							local bossName = lootData.bossName or "Unknown"
							if not bossesSet[bossName] then
								bossesSet[bossName] = true
								table.insert(bosses, {text = bossName, value = bossName})
							end
						end
					end
				end
			end
		end

		-- Сортируем боссов по имени, но Unknown всегда в конце
		table.sort(bosses, function(a, b)
			if a.value == "all" then return true end
			if b.value == "all" then return false end
			if a.value == "Unknown" then return false end
			if b.value == "Unknown" then return true end
			return a.text < b.text
		end)

		return bosses
	end

	-- Создаем выпадающий список
	lootFrame.bossDropdown = StdUi:Dropdown(filterPanel, 200, 20, lootFrame:GetBossList())
	lootFrame.bossDropdown:SetPoint("LEFT", bossLabel, "RIGHT", 5, 0)
	lootFrame.bossDropdown.optsFrame.itemListHeight = 400 -- Устанавливаем максимальную высоту списка
	lootFrame.bossDropdown:SetValue("all")

	-- Обработчик изменения текста в поле поиска
	searchBox:SetScript("OnTextChanged", function(self_searchBox) -- self здесь это searchBox
		local text = self_searchBox:GetText()
		if text == "" then
			self_searchBox.placeholder:Show()
		else
			self_searchBox.placeholder:Hide()
		end
		-- Обновляем список при изменении текста
		lootFrame:UpdateLootList(lootFrame.bossDropdown:GetValue(), text)
	end)

	-- Скрываем placeholder при фокусе
	searchBox:SetScript("OnEditFocusGained", function(self_searchBox)
		self_searchBox.placeholder:Hide()
	end)

	-- Показываем placeholder при потере фокуса, если поле пустое
	searchBox:SetScript("OnEditFocusLost", function(self_searchBox)
		if self_searchBox:GetText() == "" then
			self_searchBox.placeholder:Show()
		end
	end)

	-- Обработчик изменения значения выпадающего списка
	lootFrame.bossDropdown.OnValueChanged = function(self_dropdown, value) -- self здесь dropdown
		-- Обновляем список при изменении значения
		lootFrame:UpdateLootList(value, lootFrame.searchBox:GetText())
	end

	local lootColumns = {
		{
			name = "Boss",
			width = 150,
			align = "LEFT",
			index = "bossName",
			defaultSort = "asc",
		},
		{
			name = "Player",
			width = 150,
			align = "LEFT",
			index = "playerName",
		},
		{
			name = "Item",
			width = 300,
			align = "LEFT",
			index = "itemLink",
			events = {
				OnEnter = function(table, cellFrame, rowFrame, rowData, columnData, rowIndex, columnIndex)
					GameTooltip:SetOwner(cellFrame, "ANCHOR_RIGHT")
					GameTooltip:SetHyperlink(rowData.itemLink)
					GameTooltip:Show()
				end,
				OnLeave = function(table, cellFrame)
					GameTooltip:Hide()
				end,
			},
		},
		{
			name = "Time",
			width = 150,
			align = "LEFT",
			index = "timestamp",
			format = function(value)
				if not value then return "" end
				-- Получаем текущее время сервера через игровое API
				local hours, minutes = GetGameTime()
				local serverTime = hours * 3600 + minutes * 60
				
				-- Получаем UTC время
				local utcTime = GetServerTime()
				local utcHours = tonumber(date("!%H", utcTime))
				local utcMinutes = tonumber(date("!%M", utcTime))
				local utcSeconds = utcHours * 3600 + utcMinutes * 60
				
				-- Вычисляем смещение для отображения
				local offset = serverTime - utcSeconds
				if offset > 43200 then -- более 12 часов
					offset = offset - 86400
				elseif offset < -43200 then
					offset = offset + 86400
				end
				
				return date("!%d.%m.%Y %H:%M:%S", value + offset)
			end,
		},
	}

	lootFrame.lootTable = StdUi:ScrollTable(lootFrame, lootColumns)
	lootFrame.lootTable:SetPoint("TOPLEFT", filterPanel, "BOTTOMLEFT", 0, -10)
	lootFrame.lootTable:SetPoint("BOTTOMRIGHT", lootFrame, "BOTTOMRIGHT", -10, 10)

	-- Настраиваем размеры таблицы
	local rowHeight = 20 -- Высота одной строки
	local availableHeight = lootFrame:GetHeight() - filterPanel:GetHeight() - 50 -- Доступная высота с учетом отступов
	local numRows = math.floor(availableHeight / rowHeight)
	
	lootFrame.lootTable:SetDisplayRows(numRows, rowHeight)

	-- Функция обновления списка лута (прикреплена к самому окну)
	function lootFrame:UpdateLootList(selectedBoss, searchText)
		-- Обновляем список боссов (опционально, если нужно динамически)
		-- local bosses = self:GetBossList() -- self здесь lootFrame
		-- self.bossDropdown:SetOptions(bosses)
		
		local data = {}
		local lastRaidID = Main.db.global.lastRaidID
		local lastRaid = Main.db.global.RaidDB[lastRaidID]

		if lastRaid and lastRaid.players then
			for playerID, playerData in pairs(lastRaid.players) do
				if playerData.playerLoot then
					for _, lootData in ipairs(playerData.playerLoot) do
						if lootData.raidID == lastRaidID then
							local currentBossName = lootData.bossName or "Unknown"
							local itemLink = lootData.itemLink
							
							-- Получаем имя предмета для сравнения
							local _, itemName = GetItemInfo(itemLink)
							itemName = itemName and itemName:lower() or "" -- Имя в нижнем регистре

							-- Получаем текст подсказки для сравнения
							local tooltipText = AlphaProtocol.LootModule:GetFullTooltipTextForItem(itemLink) -- Используем метод модуля

							-- Проверяем, не находится ли предмет в черном списке
							local isBlacklisted = false
							if Main.db.global.LootBlacklist then
								for _, blacklistedString in ipairs(Main.db.global.LootBlacklist) do
									local searchString = blacklistedString:lower()
									-- Проверяем наличие строки из черного списка В ИМЕНИ или В ТЕКСТЕ ПОДСКАЗКИ
									if searchString ~= "" and (itemName:find(searchString, 1, true) or tooltipText:find(searchString, 1, true)) then
										isBlacklisted = true
										break
									end
								end
							end

							-- Фильтруем по выбранному боссу, поисковому запросу и черному списку
							if not isBlacklisted and
							   (selectedBoss == "all" or selectedBoss == currentBossName) and
							   (not searchText or searchText == "" or 
							    currentBossName:lower():find(searchText:lower(), 1, true) or
							    playerData.playerName:lower():find(searchText:lower(), 1, true) or
							    (itemName and itemName:find(searchText:lower(), 1, true))) then -- Поиск по имени предмета
								table.insert(data, { 
									bossName = currentBossName,
									playerName = playerData.playerName, 
									itemLink = lootData.itemLink,
									timestamp = lootData.timestamp
								})
							end
						end
					end
				end
			end
		end

		-- Сортировка сначала по боссам, потом по времени получения (сначала новые)
		table.sort(data, function(a, b)
			if a.bossName == b.bossName then
				if a.timestamp == b.timestamp then
					return a.playerName < b.playerName
				end
				return (a.timestamp or 0) > (b.timestamp or 0)
			end
			return a.bossName < b.bossName
		end)
		
		lootFrame.lootTable:SetData(data)
		lootFrame.lootTable:Refresh()
	end

	-- Сохраняем созданное окно в модуле и возвращаем его
	self.lootFrame = lootFrame
	return self.lootFrame
end

-- Функция создания окна черного списка
function LootModule:CreateBlacklistWindow()
	local StdUi = LibStub("StdUi")
	
	if self.blacklistFrame and self.blacklistFrame:IsShown() then
		self.blacklistFrame:Hide()
		return
	end

	if not Main.db.global.LootBlacklist then
		Main.db.global.LootBlacklist = {}
	end

	local frame = StdUi:Window(UIParent, 400, 600, "Черный список")
	frame:SetPoint("CENTER")
	frame:SetFrameLevel(AlphaProtocol.InterfaceModule:GetNextFrameLevel()) -- Используем общую систему для уровней фреймов
	self.blacklistFrame = frame
	
	-- Добавляем возможность закрытия окна по ESC
	AlphaProtocol.InterfaceModule:EnableEscapeKey(frame)

	-- Создаем таблицу для отображения предметов в черном списке
	local columns = {
		{
			name = "Название",
			width = 250,
			align = "LEFT",
			index = "name",
			format = "text",
		},
		{
			name = "Удалить",
			width = 100,
			align = "CENTER",
			index = "delete",
			format = "custom",
			renderer = function(cell, value, row)
				if not cell.button then
					cell.button = StdUi:Button(cell, 80, 20, "Удалить")
					cell.button:SetPoint("CENTER")
					local module = self -- сохраняем ссылку на модуль
					cell.button.row = row -- сохраняем ссылку на строку в кнопке
					cell.button:SetScript("OnClick", function(button)
						local currentRow = button:GetParent().row -- получаем актуальную строку из родительской ячейки
						for i, item in ipairs(Main.db.global.LootBlacklist) do
							if item == currentRow.name then
								table.remove(Main.db.global.LootBlacklist, i)
								break
							end
						end
						if frame and frame.itemTable then
							frame.itemTable:SetData(module:GetBlacklistData())
							frame.itemTable:Refresh()
						end
					end)
				end
				
				cell.row = row -- обновляем ссылку на строку в ячейке
				cell.button:Show()
			end,
		}
	}

	frame.itemTable = StdUi:ScrollTable(frame, columns)
	frame.itemTable:SetPoint("TOPLEFT", frame, "TOPLEFT", 10, -30)
	frame.itemTable:SetPoint("RIGHT", frame, "RIGHT", -10, 0)
	frame.itemTable:SetHeight(300)

	-- Создаем поле ввода и кнопку для добавления новых предметов
	local addItemBox = StdUi:SimpleEditBox(frame, 250, 25, "")
	addItemBox:SetPoint("TOPLEFT", frame.itemTable, "BOTTOMLEFT", 0, -10)

	local addButton = StdUi:Button(frame, 100, 25, "Добавить")
	addButton:SetPoint("LEFT", addItemBox, "RIGHT", 10, 0)

	-- Создаем панель для импорта/экспорта
	local importExportPanel = StdUi:Panel(frame, frame:GetWidth() - 20, 200)
	importExportPanel:SetPoint("TOPLEFT", addItemBox, "BOTTOMLEFT", 0, -10)

	-- Добавляем текстовое поле для импорта/экспорта
	local importExportBox = StdUi:MultiLineBox(importExportPanel, importExportPanel:GetWidth() - 20, 130, "")
	importExportBox:SetPoint("TOP", importExportPanel, "TOP", 0, -10)

	-- Кнопки импорта и экспорта
	local exportButton = StdUi:Button(importExportPanel, 100, 25, "Экспорт")
	exportButton:SetPoint("BOTTOMLEFT", importExportPanel, "BOTTOMLEFT", 10, 10)
	
	local importButton = StdUi:Button(importExportPanel, 100, 25, "Импорт")
	importButton:SetPoint("LEFT", exportButton, "RIGHT", 10, 0)

	-- Функция для экспорта
	local function ExportBlacklist()
		local exportString = table.concat(Main.db.global.LootBlacklist, ";")
		importExportBox:SetText(exportString)
	end

	-- Функция для импорта
	local function ImportBlacklist()
		local importString = importExportBox:GetText()
		if importString and importString ~= "" then
			-- Очищаем текущий черный список
			wipe(Main.db.global.LootBlacklist)
			
			-- Разбиваем строку по разделителю и добавляем элементы
			for item in string.gmatch(importString, "[^;]+") do
				table.insert(Main.db.global.LootBlacklist, item)
			end
			
			-- Обновляем таблицу
			frame.itemTable:SetData(self:GetBlacklistData())
			frame.itemTable:Refresh()
		end
	end

	exportButton:SetScript("OnClick", ExportBlacklist)
	importButton:SetScript("OnClick", ImportBlacklist)

	local function addItem()
		local itemName = addItemBox:GetText()
		if itemName and itemName ~= "" then
			table.insert(Main.db.global.LootBlacklist, itemName)
			addItemBox:SetText("")
			frame.itemTable:SetData(self:GetBlacklistData())
		end
	end

	addItemBox:SetScript("OnEnterPressed", addItem)
	addButton:SetScript("OnClick", addItem)

	frame.itemTable:SetData(self:GetBlacklistData())
end

-- Функция для получения данных черного списка
function LootModule:GetBlacklistData()
	local data = {}
	if Main.db.global.LootBlacklist then
		for _, itemName in ipairs(Main.db.global.LootBlacklist) do
			table.insert(data, {
				name = itemName,
				delete = true
			})
		end
	end
	return data
end

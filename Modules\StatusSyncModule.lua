local AlphaProtocol = LibStub("AceAddon-3.0"):GetAddon("FriendshipIsMagic")
local StatusSyncModule = AlphaProtocol:NewModule("StatusSyncModule", "AceEvent-3.0")
local Main = AlphaProtocol:GetModule("Main")
local OtherModule -- Будет инициализирован в OnEnable
local ManagmentModule -- Будет инициализирован в OnEnable

-- Библиотеки для сжатия и сериализации данных
local aceComm = LibStub:GetLibrary("AceComm-3.0", true)
local AceSerializer = LibStub("AceSerializer-3.0", true)
local LibDeflate = LibStub:GetLibrary("LibDeflate", true)

-- Константы для синхронизации
local SYNC_PREFIX = "FIMStatusSync"
local SPLIT_MODE_CHUNK_SIZE = 200 -- Размер чанка для сплит-режима
local STATUS_SYNC_TIMEOUT = 2 -- Тайм-аут в секундах между отправками статусов
local lastSyncTime = 0 -- Время последней отправки статуса
local pendingStatusChanges = {} -- Хранит изменения статусов до отправки
local playersStatusCache = {} -- Кэш статусов игроков для отслеживания изменений
local syncTaskScheduled = false -- Флаг, указывающий, запланирована ли задача синхронизации

-- Константы для системы пинга
local PING_PREFIX = "FIMPing"
local PING_TIMEOUT = 1 * 60 -- 1 минута в секундах
local PING_CHANNEL_TIMEOUT = 1 -- Тайм-аут в секундах между проверками каналов
local pingResult = nil
local pingExpireTime = 0
local isPingSending = false
local pingChannelQueue = {}
local currentPingData = nil
local pingExpireTimer = nil -- Таймер для автоматической очистки результата пинга

-- Добавим переменные для хранения отложенных данных статусов
local pendingStatusDataQueue = {}

-- Таблица для хранения таймеров сброса статуса WAITING_SPLIT
local waitingSplitTimers = {}

function StatusSyncModule:OnEnable()
    OtherModule = AlphaProtocol:GetModule("OtherModule")
    ManagmentModule = AlphaProtocol:GetModule("ManagmentModule")
    
    -- Регистрируем prefix для коммуникации
    aceComm:RegisterComm(SYNC_PREFIX, function(...)
        self:HandleSyncMessage(...)
    end)
    C_ChatInfo.RegisterAddonMessagePrefix(SYNC_PREFIX)
    
    -- Создаем хук на функцию UpdateMainWindow для отслеживания изменений статусов
    self:HookStatusChanges()
    
    -- Инициализация кэша статусов
    self:InitializeStatusCache()
    
    -- Инициализация системы пинга
    self:InitializePingSystem()
end

-- Инициализация кэша статусов игроков
function StatusSyncModule:InitializeStatusCache()
    playersStatusCache = {}
    
    if not ManagmentModule.playersToInvite then
        return
    end
    
    for _, playerInfo in ipairs(ManagmentModule.playersToInvite) do
        if playerInfo.playerID then
            playersStatusCache[playerInfo.playerID] = playerInfo.status or ""
        end
    end
end

-- Устанавливаем хук для отслеживания изменений статусов игроков
function StatusSyncModule:HookStatusChanges()
    -- Хукаем SavePlayersToRaidDB для отслеживания сохранения статусов
    local originalSavePlayersToRaidDB = Main.SavePlayersToRaidDB
    Main.SavePlayersToRaidDB = function(...)
        -- Проверяем изменения статусов перед сохранением
        StatusSyncModule:CheckStatusChanges()
        -- Вызываем оригинальную функцию
        return originalSavePlayersToRaidDB(...)
    end
    
    -- Хукаем UpdateMainWindow для отслеживания обновления интерфейса
    if ManagmentModule then
        local originalUpdateMainWindow = ManagmentModule.UpdateMainWindow
        ManagmentModule.UpdateMainWindow = function(...)
            -- Сначала вызываем оригинальную функцию
            local result = originalUpdateMainWindow(...)
            -- Проверяем изменения статусов после обновления
            StatusSyncModule:CheckStatusChanges()
            return result
        end
    end
end

-- Проверка изменений статусов игроков
function StatusSyncModule:CheckStatusChanges()
    local hasChanges = false

    -- Проверяем все статусы игроков на изменения
    for _, playerInfo in ipairs(ManagmentModule.playersToInvite) do
        if playerInfo.playerID then
            local currentStatus = playerInfo.status or ""
            local cachedStatus = playersStatusCache[playerInfo.playerID] or ""

            -- Проверяем, изменился ли статус и является ли он IN_RAID, OFFLINE_IN_RAID или OTHER_RAID
            if currentStatus ~= cachedStatus and
               (currentStatus == ManagmentModule.strings.IN_RAID or
                currentStatus == ManagmentModule.strings.OFFLINE_IN_RAID or
                currentStatus == ManagmentModule.strings.OTHER_RAID) then

                -- Добавляем изменение в очередь
                pendingStatusChanges[playerInfo.playerID] = {
                    playerID = playerInfo.playerID,
                    status = currentStatus, -- Отправляем сам статус
                    name = playerInfo.name
                }

                -- Обновляем кэш
                playersStatusCache[playerInfo.playerID] = currentStatus
                hasChanges = true
            end
        end
    end

    -- Если есть изменения, планируем отправку
    if hasChanges and not syncTaskScheduled then
        self:ScheduleStatusSync()
    end
end

-- Планирование отправки статусов
function StatusSyncModule:ScheduleStatusSync()
    if syncTaskScheduled then
        return
    end
    
    syncTaskScheduled = true
    C_Timer.After(STATUS_SYNC_TIMEOUT, function()
        StatusSyncModule:SendPendingStatusChanges()
        syncTaskScheduled = false
    end)
end

-- Отправка всех изменений статусов
function StatusSyncModule:SendPendingStatusChanges()
    -- Если список изменений пуст, выходим
    if not next(pendingStatusChanges) then
        return
    end
    
    -- Создаем структуру данных для отправки
    local statusData = {
        type = "StatusSync",
        changes = pendingStatusChanges,
        sender = UnitGUID("player"),
        timestamp = time()
    }
    
    -- Отправляем изменения
    self:SendStatusData(statusData)
    
    -- Выводим информацию о синхронизации
    local count = 0
    for _ in pairs(pendingStatusChanges) do count = count + 1 end
   -- print("|cFF00FF00[Синхронизация статусов]|r Отправлены изменения статусов " .. count .. " игроков.")
    
    -- Очищаем список изменений
    pendingStatusChanges = {}
end

-- Функция для сериализации и отправки статусов
function StatusSyncModule:SendStatusData(statusData)
    if not statusData or not next(statusData.changes) then
        return
    end
    
    -- Проверка наличия библиотек
    if not aceComm or not AceSerializer or not LibDeflate then
        print("|cFFFF0000[Синхронизация статусов]|r Ошибка: не удалось загрузить необходимые библиотеки")
        return
    end
    
    -- Текущее время
    local currentTime = GetTime()
    
    -- Проверяем тайм-аут между отправками
    if currentTime - lastSyncTime < STATUS_SYNC_TIMEOUT then
        return
    end
    
    -- Получаем оптимальный канал отправки из системы пинга
    local channel = self:GetPingResultChannel()
    
    -- Проверяем, является ли канал GUILD
    if channel ~= "GUILD" then
        -- Если канал не GUILD, не отправляем статусы и не откладываем
        -- print("|cFFFF9900[Синхронизация статусов]|r Канал не GUILD ('" .. tostring(channel) .. "'), синхронизация статусов отменена.")
        return
    end
    
    -- Проверяем наличие активного результата пинга
    if not channel then -- Эта проверка теперь избыточна из-за предыдущей, но оставим на всякий случай
        -- Если пинг не отправляется в данный момент, начинаем новый пинг
        if not isPingSending then
            -- print("|cFFFF9900[Синхронизация статусов]|r Нет активного канала связи. Определяем оптимальный канал...")
            -- Сохраняем данные для отправки после получения результата пинга
            -- Отправку откладываем только если канал может стать GUILD в будущем
            -- local serializedCopy = AceSerializer:Serialize(statusData)
            -- if serializedCopy then
            --     table.insert(pendingStatusDataQueue, serializedCopy)
            --     print("|cFFFF9900[Синхронизация статусов]|r Отправка отложена до получения канала связи")
            -- end
            self:SendPing()
        else
            -- Если пинг уже отправляется, не откладываем, т.к. требуется только GUILD
            -- print("|cFFFF9900[Синхронизация статусов]|r Пинг уже отправляется, ожидаем определения канала GUILD.")
        end
        return
    end
    
    -- Если запущен новый пинг, не откладываем
    if isPingSending then
        -- print("|cFFFF9900[Синхронизация статусов]|r Отправка отложена до получения канала связи")
        return
    end
    
    lastSyncTime = currentTime
    
    -- Сериализация данных
    local success, serializedData = pcall(function()
        return AceSerializer:Serialize(statusData)
    end)
    
    if not success or not serializedData then
        print("|cFFFF0000[Синхронизация статусов]|r Ошибка сериализации данных")
        return
    end
    
    -- Сжатие данных
    local compressedData = LibDeflate:CompressDeflate(serializedData, {
        level = 9, -- Максимальный уровень сжатия
    })
    
    if not compressedData then
        print("|cFFFF0000[Синхронизация статусов]|r Ошибка сжатия данных")
        return
    end
    
    -- Кодирование данных для безопасной передачи
    local encodedData = LibDeflate:EncodeForWoWAddonChannel(compressedData)
    
    if not encodedData then
        print("|cFFFF0000[Синхронизация статусов]|r Ошибка кодирования данных")
        return
    end
    
    -- Добавление контрольной суммы
    local checksum = self:CalculateChecksum(encodedData)
    encodedData = "{START}" .. checksum .. "#" .. encodedData .. "{END}"
    
    -- Отправка данных чанками через канал GUILD
    self:SendDataInChunks(encodedData, "GUILD", nil) -- Целевой игрок не нужен для GUILD
end

-- Отправка данных по частям
function StatusSyncModule:SendDataInChunks(encodedData, channel, target)
    local totalChunks = math.ceil(#encodedData / SPLIT_MODE_CHUNK_SIZE)
    local messageID = tostring(time()) .. "_" .. math.random(1000, 9999)
    
    for i = 1, #encodedData, SPLIT_MODE_CHUNK_SIZE do
        local chunk = encodedData:sub(i, i + SPLIT_MODE_CHUNK_SIZE - 1)
        local currentChunk = math.ceil(i/SPLIT_MODE_CHUNK_SIZE)
        
        -- Добавляем метаданные к чанку
        local chunkWithMeta = string.format("STATUS_CHUNK:%s:%d:%d:", messageID, currentChunk, totalChunks) .. chunk
        
        -- Задержка между отправками чанков
        C_Timer.After((currentChunk - 1) * 0.1, function()
            C_ChatInfo.SendAddonMessage(SYNC_PREFIX, chunkWithMeta, channel, target)
        end)
    end
end

-- Расчет контрольной суммы
function StatusSyncModule:CalculateChecksum(data)
    local sum = 0
    for i = 1, #data do
        sum = sum + string.byte(data:sub(i,i))
    end
    return string.format("%x", sum % 65536)
end

-- Обработка буферов сообщений
local messageBuffers = {}
local expectedChunks = {}
local currentChunkCount = {}
local lastUpdateTime = {}

-- Обработка входящих сообщений
function StatusSyncModule:HandleSyncMessage(prefix, message, channel, sender)
    if prefix ~= SYNC_PREFIX then return end
    
    -- Очищаем старые буферы
    self:CleanupOldBuffers()
    
    -- Проверяем формат чанка
    local messageID, chunkNum, totalChunks, chunkData = message:match("^STATUS_CHUNK:([^:]+):(%d+):(%d+):(.+)")
    if messageID and chunkNum and totalChunks and chunkData then
        chunkNum = tonumber(chunkNum)
        totalChunks = tonumber(totalChunks)
        
        -- Создаем уникальный ключ для буфера
        local bufferKey = sender .. "_" .. messageID
        
        -- Обновляем время последнего обновления
        lastUpdateTime[bufferKey] = GetTime()
        
        -- Инициализация буфера при получении первого чанка
        if chunkNum == 1 then
            messageBuffers[bufferKey] = {}
            currentChunkCount[bufferKey] = 0
            expectedChunks[bufferKey] = totalChunks
        end
        
        -- Проверяем, существует ли буфер
        if not messageBuffers[bufferKey] then
            return
        end
        
        -- Сохраняем чанк
        messageBuffers[bufferKey][chunkNum] = chunkData
        currentChunkCount[bufferKey] = (currentChunkCount[bufferKey] or 0) + 1
        
        -- Проверяем, получены ли все чанки
        if currentChunkCount[bufferKey] == totalChunks then
            local allChunksReceived = true
            for i = 1, totalChunks do
                if not messageBuffers[bufferKey][i] then
                    allChunksReceived = false
                    break
                end
            end
            
            if allChunksReceived then
                local fullMessage = table.concat(messageBuffers[bufferKey])
                
                -- Очищаем буфер
                messageBuffers[bufferKey] = nil
                currentChunkCount[bufferKey] = nil
                expectedChunks[bufferKey] = nil
                lastUpdateTime[bufferKey] = nil
                
                -- Обрабатываем полное сообщение
                self:ProcessFullMessage(fullMessage, sender)
            end
        end
    end
end

-- Очистка старых буферов
function StatusSyncModule:CleanupOldBuffers()
    local currentTime = GetTime()
    local BUFFER_TIMEOUT = 10 -- Тайм-аут в секундах
    
    for bufferKey, lastTime in pairs(lastUpdateTime) do
        if currentTime - lastTime > BUFFER_TIMEOUT and
           (not currentChunkCount[bufferKey] or currentChunkCount[bufferKey] == expectedChunks[bufferKey]) then
            messageBuffers[bufferKey] = nil
            currentChunkCount[bufferKey] = nil
            expectedChunks[bufferKey] = nil
            lastUpdateTime[bufferKey] = nil
        end
    end
end

-- Обработка полного сообщения
function StatusSyncModule:ProcessFullMessage(fullMessage, sender)
    -- Проверка целостности сообщения
    if not fullMessage:find("{START}") or not fullMessage:find("{END}") then
        return
    end
    
    -- Извлечение контрольной суммы и данных
    local checksum, encodedData = fullMessage:match("{START}([^#]+)#(.+){END}")
    if not checksum or not encodedData then
        return
    end
    
    -- Проверка контрольной суммы
    local calculatedChecksum = self:CalculateChecksum(encodedData)
    if calculatedChecksum ~= checksum then
        return
    end
    
    -- Декодирование данных
    local compressedData = LibDeflate:DecodeForWoWAddonChannel(encodedData)
    if not compressedData then 
        return 
    end
    
    -- Распаковка данных
    local uncompressedString = LibDeflate:DecompressDeflate(compressedData)
    if not uncompressedString then 
        return 
    end
    
    -- Десериализация данных
    local success, statusData = AceSerializer:Deserialize(uncompressedString)
    if not success or not statusData then 
        return 
    end
    
    -- Проверяем, что это не наше собственное сообщение
    local myIdentifier = UnitGUID("player")
    if statusData.sender == myIdentifier then 
        return 
    end
    
    -- Обрабатываем изменения статусов
    self:ApplyStatusChanges(statusData.changes)
end

-- Применение изменений статусов
function StatusSyncModule:ApplyStatusChanges(changes)
    if not changes or not next(changes) then
        return
    end
    
    -- Флаг для отключения проверки изменений во время применения полученных обновлений
  
    
    local updateNeeded = false
    local changedPlayers = 0
    local changedInfo = {}
    
    -- Создаем таблицу текущих участников рейда
    local raidMembers = {}
    if IsInRaid() then
        for i = 1, GetNumGroupMembers() do
            local name, _, _, _, _, _, _, _, _, _, _ = GetRaidRosterInfo(i)
            if name then
                raidMembers[name:lower()] = true
            end
        end
    end
    
    -- Проверяем, находится ли отправитель в нашем рейде

    
    -- Применяем изменения к игрокам
    for _, playerInfo in ipairs(ManagmentModule.playersToInvite) do
        local change = changes[playerInfo.playerID]
        if change then
            local newStatus = change.status
            local playerID = playerInfo.playerID -- Сохраняем ID для таймера

            -- Отменяем существующий таймер для этого игрока, если он есть
            if waitingSplitTimers[playerID] then
                waitingSplitTimers[playerID]:Cancel()
                waitingSplitTimers[playerID] = nil
            end

            -- Если получен статус In Raid, проверяем наличие игрока в нашем рейде
            if newStatus == ManagmentModule.strings.IN_RAID and not raidMembers[playerInfo.name:lower()] then
                newStatus = ManagmentModule.strings.OTHER_RAID
            end
            
            if playerInfo.status ~= newStatus then
                playerInfo.status = newStatus
                playersStatusCache[playerID] = newStatus -- Используем сохраненный ID
                updateNeeded = true
                changedPlayers = changedPlayers + 1
                -- Сохраняем информацию об изменении
                table.insert(changedInfo, {name = playerInfo.name, status = newStatus})

                -- Если установлен статус WAITING_SPLIT, запускаем таймер на сброс
                if newStatus == ManagmentModule.strings.WAITING_SPLIT then
                    waitingSplitTimers[playerID] = C_Timer.After(60, function()
                        -- Повторно ищем игрока, т.к. playerInfo может быть устаревшим
                        local currentPlayerInfo = ManagmentModule:GetTrackedPlayer(playerInfo.name) -- Используем GetTrackedPlayer для поиска по имени
                        if currentPlayerInfo and currentPlayerInfo.status == ManagmentModule.strings.WAITING_SPLIT then
                            currentPlayerInfo.status = ManagmentModule.strings.EXPIRED
                            playersStatusCache[playerID] = ""
                            ManagmentModule:UpdateMainWindow() -- Обновляем интерфейс
                            Main:SavePlayersToRaidDB() -- Сохраняем изменения
                            -- print("DEBUG: Статус WAITING_SPLIT для " .. currentPlayerInfo.name .. " сброшен по тайм-ауту.")
                        end
                        -- Удаляем таймер из таблицы после выполнения
                        waitingSplitTimers[playerID] = nil
                    end)
                end
            end
        end
    end
    
    -- Если были изменения, обновляем интерфейс и сохраняем изменения
    if updateNeeded then
     --   print("|cFF00FFFF[Синхронизация статусов]|r Получены изменения статусов для " .. changedPlayers .. " игроков.")
        
        -- Выводим информацию о каждом изменении
        for _, info in ipairs(changedInfo) do
         --   print("|cFF88CCFF[Игрок]|r " .. info.name .. " - |cFFFFCC00Статус:|r " .. info.status)
        end
        
        -- Временно отключаем хук проверки изменений перед обновлением окна

        
        -- Обновляем окно
        ManagmentModule:UpdateMainWindow()
        
        -- Восстанавливаем оригинальный хук

        
        Main:SavePlayersToRaidDB()
    end
end

-- Функция для ручной синхронизации всех статусов
function StatusSyncModule:ForceSyncAllStatuses()
    -- Создаем изменения для всех игроков
    local allChanges = {}
    for _, playerInfo in ipairs(ManagmentModule.playersToInvite) do
        if playerInfo.playerID then
            allChanges[playerInfo.playerID] = {
                playerID = playerInfo.playerID,
                status = playerInfo.status or "",
                name = playerInfo.name
            }
        end
    end
    
    -- Если есть игроки для синхронизации
    if next(allChanges) then
        local statusData = {
            type = "StatusSync",
            changes = allChanges,
            sender = UnitGUID("player"),
            timestamp = time()
        }
        
        -- Отправляем все статусы
        self:SendStatusData(statusData)
      --  print("|cFF00FF00[Синхронизация статусов]|r Отправлена синхронизация статусов всех игроков.")
    else
     --   print("|cFFFF9900[Синхронизация статусов]|r Нет игроков для синхронизации.")
    end
end

-- Инициализация системы пинга
function StatusSyncModule:InitializePingSystem()
    -- Регистрируем prefix для пинга
    aceComm:RegisterComm(PING_PREFIX, function(...)
        self:HandlePingMessage(...)
    end)
    C_ChatInfo.RegisterAddonMessagePrefix(PING_PREFIX)
    
    -- Регистрируем слэш-команды
    self:RegisterSlashCommands()
end

-- Регистрация слэш-команд
function StatusSyncModule:RegisterSlashCommands()
    SLASH_FIMPING1 = "/fimping"
    SlashCmdList["FIMPING"] = function(msg)
        local command, rest = msg:match("^(%S*)%s*(.-)$")
        if command == "show" then
            StatusSyncModule:ShowPingResult()
        elseif command == "clear" then
            StatusSyncModule:ClearPingResult()
        elseif command == "w" then
            local target = rest:match("^%s*(.-)%s*$") -- Убираем пробелы в начале и конце
            if target == "clear" then
                Main.db.profile.splitModeTarget = nil
                print("|cFF00FF00[FIM]|r Получатель для канала шёпота очищен")
            elseif target and target ~= "" then
                Main.db.profile.splitModeTarget = target
                print("|cFF00FF00[FIM]|r Установлен получатель для канала шёпота:", target)
            else
             --   print("|cFFFF0000[FIM]|r Использование: /fimping w ник")
            end
        else
            StatusSyncModule:SendPing()
        end
    end
end

-- Отправка пинга по всем доступным каналам
function StatusSyncModule:SendPing()
    -- Если уже идет отправка пинга, просто выходим
    if isPingSending then
      --  print("|cFFFF9900[FIM Пинг]|r Отправка пинга уже выполняется, пожалуйста, подождите")
        return
    end
    
    -- Подготавливаем данные пинга
    local pingData = {
        type = "Ping",
        sender = UnitGUID("player"),
        senderName = UnitName("player"),
        timestamp = time()
    }
    
    -- Сериализация данных
    local success, serializedData = pcall(function()
        return AceSerializer:Serialize(pingData)
    end)
    
    if not success or not serializedData then
      --  print("|cFFFF0000[FIM Пинг]|r Ошибка сериализации данных")
        return
    end
    
    -- Сжатие данных
    local compressedData = LibDeflate:CompressDeflate(serializedData, {
        level = 9,
    })
    
    if not compressedData then
     --   print("|cFFFF0000[FIM Пинг]|r Ошибка сжатия данных")
        return
    end
    
    -- Кодирование данных для безопасной передачи
    local encodedData = LibDeflate:EncodeForWoWAddonChannel(compressedData)
    
    if not encodedData then
      --  print("|cFFFF0000[FIM Пинг]|r Ошибка кодирования данных")
        return
    end
    
    -- Форматирование сообщения пинга
    local message = "PING:" .. encodedData
    
    -- Устанавливаем флаг отправки и очищаем результат
    isPingSending = true
    pingResult = nil
    
    -- Сохраняем сообщение пинга
    currentPingData = message
    
    -- Список каналов для одновременной отправки (ВОССТАНОВЛЕНО)
    local channels = {"RAID", "GUILD", "WHISPER"}

    -- Начинаем одновременную отправку по всем каналам
  --  print("|cFF00FFFF[FIM Пинг]|r Начинаем одновременную отправку пинга по каналам RAID и GUILD")

    -- Устанавливаем таймер для автоматического завершения, если не получен ответ
    C_Timer.After(PING_CHANNEL_TIMEOUT, function()
        if isPingSending then
            isPingSending = false
          --  print("|cFFFF9900[FIM Пинг]|r Не получено ответов за " .. PING_CHANNEL_TIMEOUT .. " секунд, используем RAID по умолчанию")
            pingResult = "RAID"
            pingExpireTime = GetTime() + PING_TIMEOUT
            self:SchedulePingResultExpiration()
            self:ProcessPendingStatusData()
        end
    end)
    
    -- Отправляем пинг по всем доступным каналам одновременно
    for _, channel in ipairs(channels) do
        -- Проверяем доступность канала
        local channelAvailable = (channel == "RAID" and IsInRaid()) or 
                               (channel == "GUILD" and IsInGuild()) or 
                               (channel == "WHISPER" and Main.db.profile.splitModeTarget and Main.db.profile.splitModeTarget ~= "")
        
        if channelAvailable then
            local success = pcall(function()
                if channel == "WHISPER" then
                    C_ChatInfo.SendAddonMessage(PING_PREFIX, currentPingData, channel, Main.db.profile.splitModeTarget)
                    
                else
                    C_ChatInfo.SendAddonMessage(PING_PREFIX, currentPingData, channel)
                end
            end)
            
            if success then
              --  print("|cFF00FFFF[FIM Пинг]|r Отправлен пинг по каналу " .. channel .. ", ожидаем ответ...")
            else
             --   print("|cFFFF9900[FIM Пинг]|r Не удалось отправить пинг по каналу " .. channel)
            end
        else
         --   print("|cFFFF9900[FIM Пинг]|r Канал " .. channel .. " недоступен, пропускаем")
        end
    end
end

-- Обработка входящего пинга
function StatusSyncModule:HandlePingMessage(prefix, message, channel, sender)
    if prefix ~= PING_PREFIX then return end
    
    -- Проверяем формат сообщения
    local messageType, encodedData = message:match("^(%w+):(.+)$")
    if not messageType or not encodedData then
        return
    end
    
    -- Если это пинг, отправляем ответ
    if messageType == "PING" then
        -- Декодирование данных
        local compressedData = LibDeflate:DecodeForWoWAddonChannel(encodedData)
        if not compressedData then return end
        
        -- Распаковка данных
        local uncompressedString = LibDeflate:DecompressDeflate(compressedData)
        if not uncompressedString then return end
        
        -- Десериализация данных
        local success, pingData = AceSerializer:Deserialize(uncompressedString)
        if not success or not pingData then return end
        
        -- Проверяем, что это не наш собственный пинг
        local myIdentifier = UnitGUID("player")
        if pingData.sender == myIdentifier then return end
        
        -- Записываем результат пинга (канал, с которого мы получили пинг)
        pingResult = channel
        pingExpireTime = GetTime() + PING_TIMEOUT
        
        -- Планируем автоматическую очистку результата
        self:SchedulePingResultExpiration()
        
        -- Отвечаем на пинг
        local responseData = {
            type = "PingResponse",
            sender = UnitGUID("player"),
            senderName = UnitName("player"),
            timestamp = time(),
            responseToSender = pingData.sender
        }
        
        -- Сериализация ответа
        local success, serializedResponse = pcall(function()
            return AceSerializer:Serialize(responseData)
        end)
        
        if not success or not serializedResponse then return end
        
        -- Сжатие ответа
        local compressedResponse = LibDeflate:CompressDeflate(serializedResponse, {
            level = 9,
        })
        
        if not compressedResponse then return end
        
        -- Кодирование ответа
        local encodedResponse = LibDeflate:EncodeForWoWAddonChannel(compressedResponse)
        
        if not encodedResponse then return end
        
        -- Отправка ответа в тот же канал
        local response = "RESPONSE:" .. encodedResponse
        if channel == "WHISPER" then
            C_ChatInfo.SendAddonMessage(PING_PREFIX, response, channel, sender)
        else
            C_ChatInfo.SendAddonMessage(PING_PREFIX, response, channel)
        end
      --  print("|cFF00FF00[FIM Пинг]|r Получен пинг от " .. pingData.senderName .. " через канал " .. channel .. ", отправлен ответ")
        
    -- Если это ответ на пинг
    elseif messageType == "RESPONSE" then
        -- Декодирование данных
        local compressedData = LibDeflate:DecodeForWoWAddonChannel(encodedData)
        if not compressedData then return end
        
        -- Распаковка данных
        local uncompressedString = LibDeflate:DecompressDeflate(compressedData)
        if not uncompressedString then return end
        
        -- Десериализация данных
        local success, responseData = AceSerializer:Deserialize(uncompressedString)
        if not success or not responseData then return end
        
        -- Проверяем, что этот ответ на наш пинг
        local myIdentifier = UnitGUID("player")
        if responseData.responseToSender ~= myIdentifier then return end
        
        -- Проверяем, что это не наш собственный ответ
        if responseData.sender == myIdentifier then return end
        
        -- Если у нас еще нет результата пинга, записываем его (первый ответивший канал)
        if isPingSending then
            isPingSending = false
            pingResult = channel
            pingExpireTime = GetTime() + PING_TIMEOUT
            
            -- Планируем автоматическую очистку результата
            self:SchedulePingResultExpiration()
            
          --  print("|cFF00FF00[FIM Пинг]|r Получен первый ответ от " .. responseData.senderName .. " через канал " .. channel)
         --   print("|cFF00FF00[FIM Пинг]|r Канал " .. channel .. " будет использоваться для синхронизации")
            
            -- Отправляем отложенные данные статусов
            self:ProcessPendingStatusData()
        end
    end
end

-- Планирование автоматической очистки результата пинга
function StatusSyncModule:SchedulePingResultExpiration()
    -- Отменяем предыдущий таймер, если он существует
    if pingExpireTimer then
        pingExpireTimer:Cancel()
        pingExpireTimer = nil
    end
    
    -- Планируем новый таймер на очистку
    pingExpireTimer = C_Timer.NewTimer(PING_TIMEOUT, function()
        if pingResult then
        --    print("|cFFFF9900[FIM Пинг]|r Время действия результата пинга истекло, результат очищен")
            pingResult = nil
            self:SendPing()
        end
    end)
end

-- Получение канала из результата пинга
function StatusSyncModule:GetPingResultChannel()
    -- Проверяем срок жизни пинга
    if pingResult and GetTime() < pingExpireTime then
        return pingResult
    end
    return nil -- Запасной вариант
end

-- Отображение результата пинга
function StatusSyncModule:ShowPingResult()
    -- Проверяем срок жизни пинга
    if pingResult and GetTime() < pingExpireTime then
       print("|cFF00FFFF[FIM Пинг]|r Активный канал связи: " .. pingResult)
    else
        print("|cFFFF9900[FIM Пинг]|r Нет активного результата пинга или истек срок действия")
        -- Очищаем истекший результат
        pingResult = nil
    end
end

-- Очистка результата пинга
function StatusSyncModule:ClearPingResult()
    pingResult = nil
    
    -- Отменяем таймер очистки, если он существует
    if pingExpireTimer then
        pingExpireTimer:Cancel()
        pingExpireTimer = nil
    end
    
  --  print("|cFF00FFFF[FIM Пинг]|r Результат пинга очищен")
   -- self:SendPing()
end

-- Функция для обработки отложенных данных статусов
function StatusSyncModule:ProcessPendingStatusData()
    if #pendingStatusDataQueue > 0 then
      --  print("|cFF00FF00[Синхронизация статусов]|r Отправка " .. #pendingStatusDataQueue .. " отложенных пакетов данных")
        
        for _, serializedData in ipairs(pendingStatusDataQueue) do
            local success, statusData = AceSerializer:Deserialize(serializedData)
            if success and statusData then
                -- Небольшая задержка между отправками
                C_Timer.After(0.5, function()
                    self:SendStatusData(statusData)
                end)
            end
        end
        
        -- Очищаем очередь
        pendingStatusDataQueue = {}
    end
end 
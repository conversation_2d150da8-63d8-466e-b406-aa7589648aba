local LibStub = LibStub
local AceAddon = LibStub("AceAddon-3.0")
local AceComm = LibStub("AceComm-3.0")
local LibDeflate = LibStub("LibDeflate")
local AceSerializer = LibStub("AceSerializer-3.0")

local AlphaProtocol = LibStub("AceAddon-3.0"):GetAddon("FriendshipIsMagic")
local DataReceiver = AlphaProtocol:NewModule("DataReceiver", "AceComm-3.0")
local Main = AlphaProtocol:GetModule("Main")

-- Префикс для коммуникации между аддонами
local COMM_PREFIX = "MULTIHELPER_SYNC"

-- Функция для форматированного вывода таблицы с типами данных
function DataReceiver:PrintTable(tbl, indent)
    if not tbl then 
        print(indent .. "nil")
        return 
    end
    indent = indent or ""
    if type(tbl) ~= "table" then
        print(indent .. "Значение(" .. type(tbl) .. "): " .. tostring(tbl))
        return
    end
    
    local count = 0
    for k, v in pairs(tbl) do
        count = count + 1
        if type(v) == "table" then
            print(indent .. tostring(k) .. " (" .. type(k) .. ") = {")
            self:PrintTable(v, indent .. "    ")
            print(indent .. "}")
        else
            print(indent .. tostring(k) .. " (" .. type(k) .. ") = " .. tostring(v) .. " (" .. type(v) .. ")")
        end
    end
    if count == 0 then
        print(indent .. "<пустая таблица>")
    end
end

-- Вспомогательная функция для получения текущего активного рейда
function DataReceiver:GetCurrentRaid()
    if not Main.db.global.RaidDB then
        return nil
    end
    
    local lastRaidID = Main.db.global.lastRaidID
    if not lastRaidID then
        return nil
    end
    
    return Main.db.global.RaidDB[lastRaidID]
end

-- Вспомогательная функция для подсчета общего количества роллов
function DataReceiver:CountTotalRolls(items)
    local totalRolls = 0
    for _, item in ipairs(items) do
        if item.rolls then
            for _, rollType in pairs(item.rolls) do
                totalRolls = totalRolls + #rollType
            end
        end
    end
    return totalRolls
end

-- Вспомогательная функция для сохранения информации о ролле предмета
function DataReceiver:SaveItemRollInfo(currentRaid, encounterName, encounterID, items)
    if not currentRaid.lootRolls then
        currentRaid.lootRolls = {}
    end
    
    -- Проверяем существующие данные
    if currentRaid.lootRolls[encounterID] then
        local existingData = currentRaid.lootRolls[encounterID]
        local existingRollsCount = self:CountTotalRolls(existingData.items)
        local newRollsCount = self:CountTotalRolls(items)
        
        -- Если новых роллов меньше или столько же, пропускаем обновление
        if newRollsCount <= existingRollsCount then
            print(string.format("Пропуск обновления для %s: существующие данные полнее (роллов: %d > %d)", 
                encounterName, existingRollsCount, newRollsCount))
            return false
        end
        
        print(string.format("Обновление данных для %s: найдено больше роллов (роллов: %d > %d)", 
            encounterName, newRollsCount, existingRollsCount))
    end
    
    -- Создаем или обновляем запись для данного энкаунтера
    currentRaid.lootRolls[encounterID] = {
        encounterName = encounterName,
        items = {},
        lastUpdate = time()
    }
    
    -- Добавляем информацию о каждом предмете
    for _, itemInfo in ipairs(items) do
        table.insert(currentRaid.lootRolls[encounterID].items, {
            itemLink = itemInfo.itemLink,
            winner = itemInfo.winner,
            rolls = itemInfo.rolls,
            needRolls = itemInfo.needRolls,
            timestamp = time()
        })
    end
    
    return true
end

function DataReceiver:OnEnable()
    self:RegisterComm(COMM_PREFIX)
 --   print("MultiHelper Receiver активирован")
end

function DataReceiver:OnCommReceived(prefix, message, distribution, sender)
    -- Выводим базовую информацию о полученном сообщении
  --  print("--------------------------------------------------")
  --  print(string.format("|cFF00FF00[DataReceiver]|r Получено сообщение:"))
  --  print(string.format("От: |cFF00FFFF%s|r", sender))
 --   print(string.format("Канал: |cFFFFFF00%s|r", distribution))
 --   print(string.format("Префикс: |cFFFF00FF%s|r", prefix))
 --   print("--------------------------------------------------")

    if prefix ~= COMM_PREFIX then 
    --    print("|cFFFF0000Неверный префикс, сообщение пропущено|r")
        return 
    end
    
    -- Декодирование данных из безопасного формата
    local decoded = LibDeflate:DecodeForWoWAddonChannel(message)
    if not decoded then
        print("|cFFFF0000Ошибка декодирования данных|r")
        return
    end
  --  print("|cFF00FF00Декодирование успешно|r")
    
    -- Распаковка сжатых данных
    local decompressed = LibDeflate:DecompressDeflate(decoded)
    if not decompressed then
        print("|cFFFF0000Ошибка распаковки данных|r")
        return
    end
  --  print("|cFF00FF00Распаковка успешна|r")
    
    -- Десериализация данных
    local success, data = AceSerializer:Deserialize(decompressed)
    if not success then
        print("|cFFFF0000Ошибка десериализации данных|r")
        return
    end
 --   print("|cFF00FF00Десериализация успешна|r")
    
    -- Получаем текущий активный рейд
    local currentRaid = self:GetCurrentRaid()
    if not currentRaid then
        print("|cFFFF0000Ошибка: нет активного рейда для сохранения данных|r")
        return
    end
 --   print(string.format("|cFF00FF00Найден активный рейд: %s|r", currentRaid.raidName or "Без имени"))
    
    -- Проверяем структуру данных и выводим подробную информацию
   -- print("--------------------------------------------------")
  --  print("|cFFFFFF00Проверка структуры данных:|r")
  --  print(string.format("Тип данных: %s", type(data)))
    if type(data) == "table" then
     --   print("Наличие необходимых полей:")
      --  print(string.format("encounterName: %s", data.encounterName and ("|cFF00FF00есть|r (" .. tostring(data.encounterName) .. ")") or "|cFFFF0000нет|r"))
      --  print(string.format("encounterID: %s", data.encounterID and ("|cFF00FF00есть|r (" .. tostring(data.encounterID) .. ")") or "|cFFFF0000нет|r"))
     --   print(string.format("items: %s", data.items and ("|cFF00FF00есть|r (количество: " .. (type(data.items) == "table" and #data.items or "не таблица") .. ")") or "|cFFFF0000нет|r"))
    end
  --  print("--------------------------------------------------")
   -- print("|cFFFFFF00Полное содержимое данных:|r")
  --  self:PrintTable(data)
  --  print("--------------------------------------------------")

    -- Проверяем, что получены данные о роллах
    if data.encounterName and data.encounterID and data.items then
    --    print("--------------------------------------------------")
    --   print(string.format("|cFFFFFF00Данные о боссе:|r %s (ID: %d)", data.encounterName, data.encounterID))
     --   print(string.format("|cFFFFFF00Количество предметов:|r %d", #data.items))
        
        -- Выводим информацию о каждом предмете
    --    print("|cFFFFFF00Список предметов:|r")
        for i, item in ipairs(data.items) do
       --     print(string.format("%d. %s", i, item.itemLink))
            if item.winner then
        --        print(string.format("   Победитель: %s (ролл: %d, тип: %d)", 
      --              item.winner.playerName, item.winner.rollValue, item.winner.rollType))
            end
            
            -- Выводим количество роллов каждого типа
            if item.rolls then
                for rollType, players in pairs(item.rolls) do
          --          print(string.format("   Тип ролла %d: %d игроков", rollType, #players))
                end
            end
        end
    --    print("--------------------------------------------------")

        -- Пытаемся сохранить информацию
        local updated = self:SaveItemRollInfo(currentRaid, data.encounterName, data.encounterID, data.items)
        
        -- Обновляем интерфейс только если данные были обновлены
        if updated then
            if AlphaProtocol.InterfaceModule and AlphaProtocol.InterfaceModule.UpdateMainWindowRaidInfo then
                AlphaProtocol.InterfaceModule:UpdateMainWindowRaidInfo()
            end
            print("|cFF00FF00" .. string.format("Saved information about rolls for %d items from boss %s", #data.items, data.encounterName) .. "|r")
        end
    else
        print("|cFFFF0000Error: wrong data format|r")
    end
 --   print("--------------------------------------------------")
end

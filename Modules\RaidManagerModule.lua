local AlphaProtocol = LibStub("AceAddon-3.0"):GetAddon("FriendshipIsMagic")
local RaidManagerModule = AlphaProtocol:NewModule("RaidManagerModule", "AceConsole-3.0")
local StdUi = LibStub("StdUi"):NewInstance()

-- Таблица соответствия ID для классических подземелий
local CLASSIC_DUNGEON_MAP_IDS = {

}

-- Функция инициализации начального списка рейдов
function RaidManagerModule:InitializeDefaultRaids()
   print("Инициализация начального списка рейдов...")
    -- ID текущих рейдов Dragonflight
    local defaultRaids = {
        {id = 1273, name = "Nerub-ar Palace"},
        {id = 1296, name = "Liberation of Undermine"},
        {id = 1302, name = "Manaforge Omega"},
    }
    -- Добавляем каждый рейд и его боссов
    for _, raidInfo in ipairs(defaultRaids) do
        if not AlphaProtocol.db.global.RaidConfigs2[raidInfo.id] then
            -- Пробуем получить информацию о рейде с проверкой на ошибки
            local success = pcall(function()
                EJ_SelectInstance(raidInfo.id)
            end)
            
            if success then
                local name, description, bgImage, buttonImage, loreImage, dungeonAreaMapID, instanceMapID = EJ_GetInstanceInfo(raidInfo.id)
                
                if name then
                    -- Получаем список боссов
                    local bosses = self:GetRaidBosses(raidInfo.id)
                    local bossesData = {}
                    
                    for _, boss in ipairs(bosses) do
                        -- Получаем имена НПС для каждого босса
                        local npcNames = self:GetNPCNames(boss.id)
                        
                        table.insert(bossesData, {
                            id = boss.id,
                            name = boss.name,
                            altNames = npcNames
                        })
                    end
                    
                    -- Добавляем рейд в конфигурацию
                    AlphaProtocol.db.global.RaidConfigs2[raidInfo.id] = {
                        id = raidInfo.id,
                        name = name,
                        mapID = instanceMapID,
                        type = "raid",
                        bosses = bossesData
                    }
                else
                   print("|cFFFF0000[FriendshipIsMagic]|r Не удалось получить информацию о рейде: " .. raidInfo.name .. " (ID: " .. raidInfo.id .. ")")
                end
            else
               print("|cFFFF0000[FriendshipIsMagic]|r Рейд недоступен в текущей версии: " .. raidInfo.name .. " (ID: " .. raidInfo.id .. ")")
            end
        end
    end
end

function RaidManagerModule:OnEnable()
    -- Инициализация базы данных рейдов при первом запуске
    if not AlphaProtocol.db.global.RaidConfigs2 then
        AlphaProtocol.db.global.RaidConfigs2 = {}
    end
    
    -- Проверяем, пуст ли список рейдов
    local isConfigEmpty = true
    for _ in pairs(AlphaProtocol.db.global.RaidConfigs2) do
        isConfigEmpty = false
        break
    end
    
    -- Если список пуст, инициализируем его
    if isConfigEmpty then
        self:InitializeDefaultRaids()
    end
end

-- Получение списка доступных рейдов через API
function RaidManagerModule:GetAvailableRaids()
    local raids = {}
    
    -- Сначала очищаем текущий выбор
    EJ_ClearSearch()
    EJ_SetDifficulty(14) -- Устанавливаем сложность на "Нормальный" режим
    
    -- Получаем текущий тир
    local currentTier = EJ_GetCurrentTier()
    
    -- Перебираем все тиры (дополнения)
    for tier = 1, EJ_GetNumTiers() do
        EJ_SelectTier(tier)
        
        -- Получаем рейды текущего тира
        local instanceIndex = 1
        local instanceID = EJ_GetInstanceByIndex(instanceIndex, true)
        
        while instanceID do
            EJ_SelectInstance(instanceID)
            local name, description, bgImage, buttonImage, loreImage, dungeonAreaMapID, instanceMapID = EJ_GetInstanceInfo(instanceID)
            
            -- Получаем правильный ID карты
            local correctMapID = CLASSIC_DUNGEON_MAP_IDS[instanceID] or instanceMapID
            
            -- Проверяем, что инстанс еще не добавлен
            local isDuplicate = false
            for _, existingRaid in ipairs(raids) do
                if existingRaid.id == instanceID then
                    isDuplicate = true
                    break
                end
            end
            
            if not isDuplicate then
                table.insert(raids, {
                    id = instanceID,
                    name = name,
                    mapID = correctMapID,
                    type = "raid",
                    tier = tier
                })
            end
            
            instanceIndex = instanceIndex + 1
            instanceID = EJ_GetInstanceByIndex(instanceIndex, true)
        end
        
        -- Получаем подземелья текущего тира
        instanceIndex = 1
        instanceID = EJ_GetInstanceByIndex(instanceIndex, false)
        
        while instanceID do
            EJ_SelectInstance(instanceID)
            local name, description, bgImage, buttonImage, loreImage, dungeonAreaMapID, instanceMapID = EJ_GetInstanceInfo(instanceID)
            
            -- Получаем правильный ID карты
            local correctMapID = CLASSIC_DUNGEON_MAP_IDS[instanceID] or instanceMapID
            
            -- Проверяем, что инстанс еще не добавлен
            local isDuplicate = false
            for _, existingDungeon in ipairs(raids) do
                if existingDungeon.id == instanceID then
                    isDuplicate = true
                    break
                end
            end
            
            if not isDuplicate then
                table.insert(raids, {
                    id = instanceID,
                    name = name,
                    mapID = correctMapID,
                    type = "dungeon",
                    tier = tier
                })
            end
            
            instanceIndex = instanceIndex + 1
            instanceID = EJ_GetInstanceByIndex(instanceIndex, false)
        end
    end
    
    -- Возвращаем текущий тир в исходное состояние
    EJ_SelectTier(currentTier)
    
    -- Сортируем список: сначала по тиру (новые первые), затем по типу (рейды первые), затем по имени
    table.sort(raids, function(a, b)
        if a.tier ~= b.tier then
            return a.tier > b.tier  -- Новые тиры первыми
        elseif a.type ~= b.type then
            return a.type < b.type  -- Рейды перед подземельями
        end
        return a.name < b.name
    end)
    
    return raids
end

-- Получение списка боссов для конкретного рейда
function RaidManagerModule:GetRaidBosses(raidID)
    local bosses = {}
    
    EJ_ClearSearch()
    EJ_SelectInstance(raidID)
    
    local i = 1
    while true do
        local name, description, bossID = EJ_GetEncounterInfoByIndex(i, raidID)
        if not name then break end
        
        table.insert(bosses, {
            id = bossID,
            name = name,  -- Используем name вместо description
            order = i
        })
        
        i = i + 1
    end
    
    return bosses
end

-- Удаление рейда
function RaidManagerModule:DeleteRaid(raidID)
    if AlphaProtocol.db.global.RaidConfigs2[raidID] then
        AlphaProtocol.db.global.RaidConfigs2[raidID] = nil
        self:UpdateRaidList()
        -- Очищаем список боссов, если был выбран удаленный рейд
        if self.selectedRaidID == raidID then
            self.selectedRaidID = nil
            if self.bossList then
                self.bossList:SetData({})
            end
        end
    end
end

-- Удаление босса
function RaidManagerModule:DeleteBoss(bossIndex)
    if not self.selectedRaidID then return end
    
    local raidConfig = AlphaProtocol.db.global.RaidConfigs2[self.selectedRaidID]
    if raidConfig and raidConfig.bosses and raidConfig.bosses[bossIndex] then
        table.remove(raidConfig.bosses, bossIndex)
        self:UpdateBossList()
    end
end

function RaidManagerModule:CloseSystemFrames()
    -- Закрываем только Settings панель через системную функцию
    if Settings and Settings.CloseUI then
        Settings.CloseUI()
    end
end

function RaidManagerModule:CreateRaidManagerWindow()
    -- Закрываем все системные окна
    self:CloseSystemFrames()
    
    if self.raidManagerWindow then
        if self.raidManagerWindow:IsShown() then
            self.raidManagerWindow:Hide()
            return
        else
            -- Устанавливаем новый максимальный уровень фрейма при показе
            self.raidManagerWindow:SetFrameStrata("FULLSCREEN_DIALOG")
            self.raidManagerWindow:SetFrameLevel(100)
            self.raidManagerWindow:Raise()
            self.raidManagerWindow:Show()
            return
        end
    end

    local window = StdUi:Window(UIParent, 800, 500, "Raid Manager")
    window:SetPoint("CENTER")
    window:SetFrameStrata("FULLSCREEN_DIALOG")
    window:SetFrameLevel(100)
    window:Raise()
    
    -- Добавляем обработчик для поддержания окна поверх других
    window:SetScript("OnShow", function(self)
        self:SetFrameStrata("FULLSCREEN_DIALOG")
        self:SetFrameLevel(100)
        self:Raise()
    end)
    
    self.raidManagerWindow = window
    
    -- Левая панель (список рейдов)
    local raidListPanel = StdUi:Panel(window, 380, 400)
    raidListPanel:SetPoint("TOPLEFT", window, "TOPLEFT", 10, -50)
    
    -- Правая панель (список боссов)
    local bossListPanel = StdUi:Panel(window, 380, 400)
    bossListPanel:SetPoint("TOPRIGHT", window, "TOPRIGHT", -40, -50)
    
    -- Заголовки панелей
    local raidListTitle = StdUi:Label(window, "Raid List", 14)
    raidListTitle:SetPoint("BOTTOMLEFT", raidListPanel, "TOPLEFT", 0, 5)
    
    local bossListTitle = StdUi:Label(window, "Boss List", 14)
    bossListTitle:SetPoint("BOTTOMLEFT", bossListPanel, "TOPLEFT", 0, 5)
    
    -- Кнопки управления рейдами
    local addRaidBtn = StdUi:Button(window, 100, 30, "Add Raid")
    addRaidBtn:SetPoint("TOPLEFT", raidListPanel, "BOTTOMLEFT", 0, -10)
    addRaidBtn:SetScript("OnClick", function() self:ShowAddRaidDialog() end)
    
    local deleteRaidBtn = StdUi:Button(window, 100, 30, "Delete Raid")
    deleteRaidBtn:SetPoint("LEFT", addRaidBtn, "RIGHT", 10, 0)
    deleteRaidBtn:SetScript("OnClick", function()
        if self.selectedRaidID then
            StaticPopup_Show("CONFIRM_DELETE_RAID", 
                AlphaProtocol.db.global.RaidConfigs2[self.selectedRaidID].name,
                nil,
                {raidID = self.selectedRaidID}
            )
        end
    end)
    
    -- Кнопки управления боссами
    local addBossBtn = StdUi:Button(window, 100, 30, "Add Boss")
    addBossBtn:SetPoint("TOPLEFT", bossListPanel, "BOTTOMLEFT", 0, -10)
    addBossBtn:SetScript("OnClick", function() self:ShowAddBossDialog() end)
    
    local deleteBossBtn = StdUi:Button(window, 100, 30, "Delete Boss")
    deleteBossBtn:SetPoint("LEFT", addBossBtn, "RIGHT", 10, 0)
    deleteBossBtn:SetScript("OnClick", function()
        local selectedIndex = self.bossList:GetSelection()
        if selectedIndex then
            local rowData = self.bossList:GetRow(selectedIndex)
            if rowData then
                StaticPopup_Show("CONFIRM_DELETE_BOSS", 
                    rowData.name,
                    nil,
                    {index = selectedIndex}
                )
            end
        end
    end)
    
    -- Создание списков
    self:CreateRaidList(raidListPanel)
    self:CreateBossList(bossListPanel)
    
    self:UpdateRaidList()
    
    -- Добавляем возможность закрытия окна по ESC
    AlphaProtocol.InterfaceModule:EnableEscapeKey(window)
    
    -- Обработчик обновления уровня фрейма
	window:SetScript("OnMouseDown", function(self)
		self:SetFrameLevel(AlphaProtocol.InterfaceModule:GetNextFrameLevel()+10)
       -- print("установлен уровень фрейма", AlphaProtocol.InterfaceModule:GetNextFrameLevel()+10)
	end)
    
    -- Создаем диалоги подтверждения удаления
    StaticPopupDialogs["CONFIRM_DELETE_RAID"] = {
        text = "Are you sure you want to delete raid %s?",
        button1 = "Yes",
        button2 = "No",
        OnAccept = function(self, data)
            RaidManagerModule:DeleteRaid(data.raidID)
        end,
        timeout = 0,
        whileDead = true,
        hideOnEscape = true,
        preferredIndex = 3,
    }
    
    StaticPopupDialogs["CONFIRM_DELETE_BOSS"] = {
        text = "Are you sure you want to delete boss %s?",
        button1 = "Yes",
        button2 = "No",
        OnAccept = function(self, data)
            RaidManagerModule:DeleteBoss(data.index)
        end,
        timeout = 0,
        whileDead = true,
        hideOnEscape = true,
        preferredIndex = 3,
    }
end

-- Создание списка рейдов
function RaidManagerModule:CreateRaidList(parent)
    local columns = {
        {
            name = "Type",
            width = 70,
            align = "LEFT",
            index = "type",
            format = "string"
        },
        {
            name = "Name",
            width = 230,
            align = "LEFT",
            index = "name",
            format = "string",
            events = {
                OnClick = function(table, cellFrame, rowFrame, rowData, columnData, rowIndex)
                    if rowData and rowData.id then
                        self:SelectRaid(rowData.id)
                    end
                end
            }
        }
    }
    
    local raidList = StdUi:ScrollTable(parent, columns, 12, 20)
    raidList:SetPoint("TOPLEFT", parent, "TOPLEFT", 10, -10)
    raidList:SetPoint("BOTTOMRIGHT", parent, "BOTTOMRIGHT", -10, 10)
    
    self.raidList = raidList
    self:UpdateRaidList()
end

-- Функция обновления списка рейдов
function RaidManagerModule:UpdateRaidList()
    if not self.raidList then return end
    
    local raidData = {}
    -- Получаем данные из базы данных
    for raidID, raidConfig in pairs(AlphaProtocol.db.global.RaidConfigs2) do
        table.insert(raidData, {
            id = raidID,
            name = raidConfig.name,
            type = raidConfig.type == "raid" and "Raid" or "Dungeon"
        })
    end
    
    -- Сортируем: сначала по типу, затем по имени
    table.sort(raidData, function(a, b)
        if a.type ~= b.type then
            return a.type < b.type
        end
        return a.name < b.name
    end)
    
    self.raidList:SetData(raidData)
end

-- Создание списка боссов
function RaidManagerModule:CreateBossList(parent)
    local columns = {
        {
            name = "#",
            width = 30,
            align = "CENTER",
            index = "order",
            format = "number"
        },
        {
            name = "Name",
            width = 220,
            align = "LEFT",
            index = "name",
            format = "string"
        },
        {
            name = "",
            width = 60,
            align = "CENTER",
            index = "edit",
            format = "string",
            events = {
                OnClick = function(table, cellFrame, rowFrame, rowData, columnData, rowIndex)
                    self:ShowEditBossDialog(rowIndex)
                end
            }
        }
    }
    
    local bossList = StdUi:ScrollTable(parent, columns, 12, 20)
    bossList:EnableSelection(true)
    bossList:SetPoint("TOPLEFT", parent, "TOPLEFT", 10, -10)
    bossList:SetPoint("BOTTOMRIGHT", parent, "BOTTOMRIGHT", -40, 10)
    
    -- Настройка цветов выделения
    bossList.highlightColor = { r = 1, g = 0.9, b = 0, a = 0.3 }  -- Желтый цвет для выделения
    bossList.selectedColor = { r = 1, g = 0.9, b = 0, a = 0.3 }   -- Тот же цвет для выбранной строки
    
    -- Кнопки перемещения
    local moveUpBtn = StdUi:Button(parent, 25, 25, "")
    moveUpBtn:SetPoint("TOPLEFT", bossList, "TOPRIGHT", 5, 0)
    local moveUpIcon = moveUpBtn:CreateTexture(nil, "ARTWORK")
    moveUpIcon:SetTexture("Interface\\BUTTONS\\UI-ScrollBar-ScrollUpButton-Up")
    moveUpIcon:SetAllPoints(moveUpBtn)
    moveUpBtn:SetScript("OnClick", function()
        local selectedIndex = bossList:GetSelection()
        if selectedIndex and selectedIndex > 1 then
            local raidConfig = AlphaProtocol.db.global.RaidConfigs2[self.selectedRaidID]
            if raidConfig and raidConfig.bosses then
                local boss = table.remove(raidConfig.bosses, selectedIndex)
                table.insert(raidConfig.bosses, selectedIndex - 1, boss)
                self:UpdateBossList()
                bossList:SetSelection(selectedIndex - 1)
            end
        end
    end)
    
    local moveDownBtn = StdUi:Button(parent, 25, 25, "")
    moveDownBtn:SetPoint("TOP", moveUpBtn, "BOTTOM", 0, -5)
    local moveDownIcon = moveDownBtn:CreateTexture(nil, "ARTWORK")
    moveDownIcon:SetTexture("Interface\\BUTTONS\\UI-ScrollBar-ScrollDownButton-Up")
    moveDownIcon:SetAllPoints(moveDownBtn)
    moveDownBtn:SetScript("OnClick", function()
        local selectedIndex = bossList:GetSelection()
        local raidConfig = AlphaProtocol.db.global.RaidConfigs2[self.selectedRaidID]
        if selectedIndex and raidConfig and raidConfig.bosses and selectedIndex < #raidConfig.bosses then
            local boss = table.remove(raidConfig.bosses, selectedIndex)
            table.insert(raidConfig.bosses, selectedIndex + 1, boss)
            self:UpdateBossList()
            bossList:SetSelection(selectedIndex + 1)
        end
    end)
    
    self.bossList = bossList
end

-- Обновление списка боссов
function RaidManagerModule:UpdateBossList()
    if not self.selectedRaidID then return end
    
    local raidConfig = AlphaProtocol.db.global.RaidConfigs2[self.selectedRaidID]
    if not raidConfig then return end
    
    local bossData = {}
    for order, boss in ipairs(raidConfig.bosses) do
        local altNamesCount = boss.altNames and #boss.altNames or 0
        local displayName = boss.name
        if altNamesCount > 0 then
            displayName = displayName .. " (" .. altNamesCount .. " alt)"
        end
        
        table.insert(bossData, {
            id = boss.id,
            name = displayName,
            order = order,
            edit = "Edit"
        })
    end
    
    self.bossList:SetData(bossData)
end

-- Выбор рейда
function RaidManagerModule:SelectRaid(raidID)
    self.selectedRaidID = raidID
    self:UpdateBossList()
end

-- Диалог добавления рейда
function RaidManagerModule:ShowAddRaidDialog()
    if self.addRaidDialog then
        -- При повторном показе устанавливаем новый уровень фрейма
        self.addRaidDialog:SetFrameStrata("FULLSCREEN_DIALOG")
        self.addRaidDialog:SetFrameLevel(AlphaProtocol.InterfaceModule:GetNextFrameLevel()+10)
        self.addRaidDialog:Raise()
        self.addRaidDialog:Show()
        return
    end
    
    -- Увеличим размер окна
    local dialog = StdUi:Window(UIParent, 400, 200, "Add New Instance")
    dialog:SetPoint("CENTER")
    dialog:SetFrameStrata("FULLSCREEN_DIALOG")
    dialog:SetFrameLevel(AlphaProtocol.InterfaceModule:GetNextFrameLevel()+10)
    dialog:Raise()
    
    -- Добавляем обработчик для поддержания окна поверх других
    dialog:SetScript("OnMouseDown", function(self)
        self:SetFrameLevel(AlphaProtocol.InterfaceModule:GetNextFrameLevel()+10)
    end)
    
    -- Добавляем возможность закрытия окна по ESC
    AlphaProtocol.InterfaceModule:EnableEscapeKey(dialog)
    
    local availableRaids = self:GetAvailableRaids()
    local items = {}
    for _, raid in ipairs(availableRaids) do
        local prefix = raid.type == "raid" and "[Raid] " or "[Dungeon] "
        table.insert(items, {
            text = prefix .. raid.name,
            value = raid.id,
            type = raid.type
        })
    end
    
    -- Добавим поле поиска
    local searchBox = StdUi:EditBox(dialog, 350, 24, "")
    searchBox:SetPoint("TOP", dialog, "TOP", 0, -50)
    
    -- Добавим placeholder текст
    searchBox.placeholder = searchBox:CreateFontString(nil, "OVERLAY", "GameFontNormal")
    searchBox.placeholder:SetPoint("LEFT", searchBox, "LEFT", 5, 0)
    searchBox.placeholder:SetText("Search...")
    searchBox.placeholder:SetTextColor(0.5, 0.5, 0.5, 1)
    
    -- Создадим более высокий выпадающий список
    local dropdown = StdUi:Dropdown(dialog, 350, 30, items)
    dropdown.itemListHeight = 400 -- Устанавливаем максимальную высоту списка
    dropdown:SetPoint("TOP", searchBox, "BOTTOM", 0, -10)
    
    -- Функция фильтрации списка
    local function filterItems(searchText)
        local filteredItems = {}
        searchText = searchText:lower()
        for _, item in ipairs(items) do
            if item.text:lower():find(searchText) then
                table.insert(filteredItems, item)
            end
        end
        dropdown:SetOptions(filteredItems)
    end
    
    -- Обработчики для поиска
    searchBox:SetScript("OnTextChanged", function(self)
        local text = self:GetText()
        if text == "" then
            self.placeholder:Show()
            dropdown:SetOptions(items)
        else
            self.placeholder:Hide()
            filterItems(text)
        end
    end)
    
    searchBox:SetScript("OnEditFocusGained", function(self)
        self.placeholder:Hide()
    end)
    
    searchBox:SetScript("OnEditFocusLost", function(self)
        if self:GetText() == "" then
            self.placeholder:Show()
        end
    end)
    
    local addButton = StdUi:Button(dialog, 100, 30, "Add")
    addButton:SetPoint("BOTTOM", dialog, "BOTTOM", 0, 10)
    addButton:SetScript("OnClick", function()
        local selectedRaid = dropdown:GetValue()
        if selectedRaid then
            local raidInfo = nil
            for _, raid in ipairs(availableRaids) do
                if raid.id == selectedRaid then
                    raidInfo = raid
                    break
                end
            end
            
            if raidInfo then
                local bosses = self:GetRaidBosses(raidInfo.id)
                local bossesData = {}
                
                for _, boss in ipairs(bosses) do
                    -- Получаем имена НПС для каждого босса
                    local npcNames = self:GetNPCNames(boss.id)
                    
                    table.insert(bossesData, {
                        id = boss.id,
                        name = boss.name,
                        altNames = npcNames -- Сразу добавляем имена НПС как альтернативные
                    })
                end
                
                AlphaProtocol.db.global.RaidConfigs2[raidInfo.id] = {
                    id = raidInfo.id,
                    name = raidInfo.name,
                    mapID = raidInfo.mapID,
                    type = raidInfo.type,
                    bosses = bossesData
                }
                
                self:UpdateRaidList()
                self:SelectRaid(raidInfo.id)
                dialog:Hide()
            end
        end
    end)
    
    -- Добавим информацию о количестве доступных инстансов
    local countLabel = StdUi:Label(dialog, string.format("Available instances: %d", #items), 12)
    countLabel:SetPoint("TOPLEFT", dropdown, "BOTTOMLEFT", 0, -5)
    
    self.addRaidDialog = dialog
end

-- Функция для получения имён НПС для выбранного босса
function RaidManagerModule:GetNPCNames(bossID)
    local npcNames = {}
    local i = 1
    while true do
        local creatureDisplayID, creatureName, creatureDescription, displayInfo = EJ_GetCreatureInfo(i, bossID)
        if not creatureDisplayID then break end
        if creatureName and not tContains(npcNames, creatureName) then
            table.insert(npcNames, creatureName)
        end
        i = i + 1
    end
    return npcNames
end

-- Диалог добавления босса
function RaidManagerModule:ShowAddBossDialog()
    if not self.selectedRaidID then
       print("Please select a raid first")
        return
    end
    
    if self.addBossDialog then
        -- При повторном показе устанавливаем новый уровень фрейма
        self.addBossDialog:SetFrameStrata("FULLSCREEN_DIALOG")
        self.addBossDialog:SetFrameLevel(AlphaProtocol.InterfaceModule:GetNextFrameLevel()+10)
        self.addBossDialog:Raise()
        self.addBossDialog:Show()
        return
    end
    
    local dialog = StdUi:Window(UIParent, 400, 300, "Add New Boss")
    dialog:SetPoint("CENTER")
    dialog:SetFrameStrata("FULLSCREEN_DIALOG")
    dialog:SetFrameLevel(AlphaProtocol.InterfaceModule:GetNextFrameLevel()+10)
    dialog:Raise()
    
    -- Добавляем обработчик для поддержания окна поверх других
    dialog:SetScript("OnMouseDown", function(self)
        self:SetFrameLevel(AlphaProtocol.InterfaceModule:GetNextFrameLevel()+10)
    end)
    
    -- Добавляем возможность закрытия окна по ESC
    AlphaProtocol.InterfaceModule:EnableEscapeKey(dialog)
    
    -- Список боссов из журнала
    local bosses = self:GetRaidBosses(self.selectedRaidID)
    local items = {}
    for _, boss in ipairs(bosses) do
        table.insert(items, {text = boss.name, value = boss.id})
    end
    
    local dropdown = StdUi:Dropdown(dialog, 350, 30, items)
    dropdown:SetPoint("TOP", dialog, "TOP", 0, -50)
    
    -- Поле для ввода альтернативных имён
    local altNamesLabel = StdUi:Label(dialog, "Alternative Names (comma-separated):", 12)
    altNamesLabel:SetPoint("TOPLEFT", dropdown, "BOTTOMLEFT", 0, -20)
    
    local altNamesBox = StdUi:MultiLineBox(dialog, 350, 100, "")
    altNamesBox:SetPoint("TOPLEFT", altNamesLabel, "BOTTOMLEFT", 0, -5)
    
    -- Обработчик изменения выбранного босса
    dropdown.OnValueChanged = function(self, value)
        local npcNames = RaidManagerModule:GetNPCNames(value)
        -- Добавляем имена НПС в поле альтернативных имён
        if #npcNames > 0 then
            -- Очищаем поле перед добавлением имен
            altNamesBox:SetText("")
            -- Добавляем имена НПС
            altNamesBox:SetText(table.concat(npcNames, ", "))
        end
    end
    
    local addButton = StdUi:Button(dialog, 100, 30, "Add")
    addButton:SetPoint("BOTTOM", dialog, "BOTTOM", 0, 10)
    addButton:SetScript("OnClick", function()
        local selectedBoss = dropdown:GetValue()
        if selectedBoss then
            local bossInfo = nil
            for _, boss in ipairs(bosses) do
                if boss.id == selectedBoss then
                    bossInfo = boss
                    break
                end
            end
            
            if bossInfo then
                local raidConfig = AlphaProtocol.db.global.RaidConfigs2[self.selectedRaidID]
                local altNames = {}
                -- Разбираем альтернативные имена
                for name in altNamesBox:GetText():gmatch("[^,]+") do
                    table.insert(altNames, strtrim(name))
                end
                
                table.insert(raidConfig.bosses, {
                    id = bossInfo.id,
                    name = bossInfo.name,
                    altNames = altNames
                })
                self:UpdateBossList()
                dialog:Hide()
            end
        end
    end)
    
    self.addBossDialog = dialog
end

-- Функция для редактирования босса
function RaidManagerModule:ShowEditBossDialog(bossIndex)
    if not self.selectedRaidID then return end
    
    local raidConfig = AlphaProtocol.db.global.RaidConfigs2[self.selectedRaidID]
    if not raidConfig or not raidConfig.bosses or not raidConfig.bosses[bossIndex] then return end
    
    local bossInfo = raidConfig.bosses[bossIndex]
    
    if self.editBossDialog then
        self.editBossDialog:Hide()
    end
    
    -- Увеличим размер окна и сделаем его более компактным
    local dialog = StdUi:Window(UIParent, 500, 250, "Edit Boss: " .. bossInfo.name)
    dialog:SetPoint("CENTER")
    dialog:SetFrameStrata("FULLSCREEN_DIALOG")
    dialog:SetFrameLevel(AlphaProtocol.InterfaceModule:GetNextFrameLevel()+10)
    dialog:Raise()
    
    -- Добавляем возможность закрытия окна по ESC
    AlphaProtocol.InterfaceModule:EnableEscapeKey(dialog)
    
    -- Поле для ввода альтернативных имён
    local altNamesLabel = StdUi:Label(dialog, "Alternative Names (comma-separated):", 12)
    altNamesLabel:SetPoint("TOPLEFT", dialog, "TOPLEFT", 20, -30)
    
    -- Увеличим размер поля для ввода
    local altNamesBox = StdUi:MultiLineBox(dialog, 460, 120, "")
    altNamesBox:SetPoint("TOPLEFT", altNamesLabel, "BOTTOMLEFT", 0, -5)
    
    -- Заполняем текущими альтернативными именами
    if bossInfo.altNames then
        altNamesBox:SetText(table.concat(bossInfo.altNames, ", "))
    end
    
    -- Получаем имена НПС для этого босса
    local function UpdateNPCNames()
        local npcNames = RaidManagerModule:GetNPCNames(bossInfo.id)
        if #npcNames > 0 then
            -- Очищаем поле перед добавлением имен
            altNamesBox:SetText("")
            -- Добавляем имена НПС
            altNamesBox:SetText(table.concat(npcNames, ", "))
        end
    end
    
    -- Создаем панель для кнопок
    local buttonPanel = StdUi:Panel(dialog, 460, 40)
    buttonPanel:SetPoint("BOTTOM", dialog, "BOTTOM", 0, 10)
    
    -- Кнопка для обновления имён НПС
    local updateNPCButton = StdUi:Button(buttonPanel, 150, 30, "По умолчанию")
    updateNPCButton:SetPoint("LEFT", buttonPanel, "LEFT", 10, 0)
    updateNPCButton:SetScript("OnClick", UpdateNPCNames)
    
    -- Кнопка сохранения
    local saveButton = StdUi:Button(buttonPanel, 100, 30, "Save")
    saveButton:SetPoint("RIGHT", buttonPanel, "RIGHT", -10, 0)
    saveButton:SetScript("OnClick", function()
        local altNames = {}
        -- Разбираем альтернативные имена
        for name in (altNamesBox:GetText() .. ","):gmatch("([^,]*),") do
            name = strtrim(name)
            if name ~= "" then
                table.insert(altNames, name)
            end
        end
        
        -- Обновляем информацию о боссе
        raidConfig.bosses[bossIndex].altNames = altNames
        
        self:UpdateBossList()
        dialog:Hide()
    end)
    
    self.editBossDialog = dialog
end 
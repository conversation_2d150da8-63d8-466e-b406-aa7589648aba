local AlphaProtocol = LibStub("AceAddon-3.0"):GetAddon("FriendshipIsMagic")
local EventsModule = AlphaProtocol:NewModule("EventsModule", "AceEvent-3.0", "AceTimer-3.0")
local Main = AlphaProtocol:GetModule("Main")

-- Обработчик событий
function EventsModule:OnEvent(V, ...)
	local updateNeeded = false
	local timeFormatted = self:GetServerTimeFormatted()
	-- Проверка события (обновление списка участников группы)
	if V == "GROUP_ROSTER_UPDATE" then
		updateNeeded = true
		if not AlphaProtocol.InterfaceModule.mainWindow then
			if AlphaProtocol.InterfaceModule then
				AlphaProtocol.InterfaceModule:CreateMainWindow()
				if AlphaProtocol.ManagmentModule then
					AlphaProtocol.ManagmentModule:UpdateMainWindow()
					if AlphaProtocol.InterfaceModule.UpdateMainWindowRaidInfo then
						AlphaProtocol.InterfaceModule:UpdateMainWindowRaidInfo()
					end
				end
			end
		elseif AlphaProtocol.InterfaceModule.mainWindow:IsVisible() then
			if AlphaProtocol.ManagmentModule then
				AlphaProtocol.ManagmentModule:UpdateMainWindow()
				if AlphaProtocol.InterfaceModule.UpdateMainWindowRaidInfo then
					AlphaProtocol.InterfaceModule:UpdateMainWindowRaidInfo()
				end
			end
		end
	-- Обработка ошибки UI для определения вражеской фракции
	elseif V == "UI_ERROR_MESSAGE" then
		local errorType, message = ...
		--print("UI_ERROR_MESSAGE:", message) -- Отладка
		if message and (message:find("Цель недружелюбна") or message:find("Target is unfriendly")) then
			-- Ищем последнего игрока, которому было отправлено приглашение
			local foundPlayer = false
			for _, playerInfo in pairs(AlphaProtocol.ManagmentModule.playersToInvite) do
				--print("Checking player:", playerInfo.name, "sendingInvite:", playerInfo.sendingInvite) -- Отладка
				if playerInfo.sendingInvite then
					--print("Found player with sendingInvite flag:", playerInfo.name) -- Отладка
					EventsModule:CancelTimer(playerInfo.timer)
					playerInfo.status = AlphaProtocol.ManagmentModule.strings.ENEMY
					playerInfo.sendingInvite = false
					--playerInfo.enemyTimestamps = playerInfo.enemyTimestamps or {}
					--table.insert(playerInfo.enemyTimestamps, timeFormatted)
					foundPlayer = true
					updateNeeded = true
					break
				end
			end
			if not foundPlayer then
				--print("No player found with sendingInvite flag") -- Отладка
			end
		elseif message and (message:find("You cannot invite a player in raid combat") or message:find("Вы не можете пригласить игрока, находясь в рейдовом режиме боя")) then
			-- Ищем последнего игрока, которому было отправлено приглашение
			local foundPlayer = false
			for _, playerInfo in pairs(AlphaProtocol.ManagmentModule.playersToInvite) do
				--print("Checking player:", playerInfo.name, "sendingInvite:", playerInfo.sendingInvite) -- Отладка
				if playerInfo.sendingInvite then
					--print("Found player in combat:", playerInfo.name) -- Отладка
					EventsModule:CancelTimer(playerInfo.timer)
					playerInfo.status = AlphaProtocol.ManagmentModule.strings.COMBAT
					playerInfo.sendingInvite = false
					playerInfo.combatTimestamps = playerInfo.combatTimestamps or {}
					table.insert(playerInfo.combatTimestamps, timeFormatted)
					AlphaProtocol.OtherModule:SendSingleTimestamp(playerInfo.playerID, "combat", timeFormatted)
					foundPlayer = true
					updateNeeded = true
					break
				end
			end
			if not foundPlayer then
				--print("No player found with sendingInvite flag for combat status") -- Отладка
			end
		end
	-- Проверка события (системные сообщения чата)
	elseif V == "CHAT_MSG_SYSTEM" then
		if AlphaProtocol.InterfaceModule.mainWindow then
			local _ = select(1, ...)

			-- Игрок из враждебной фракции
			if
				_:find("You have suggested that ")
				or _:find(" приглашается в вашу группу.")
				or _:find("You have invited ")
			then
				local suggestedPlayer = _:match("You have suggested that (.+) join your group%.")
					or _:match("(.+) приглашается в вашу группу%.")
					or _:match("You have invited (.+) to join your group.")
				--print("DEBUG Invite - Original player name:", suggestedPlayer)
				local W = AlphaProtocol.ManagmentModule:EnsureFullName(suggestedPlayer):lower()
				--print("DEBUG Invite - Full player name:", W)
				local X = AlphaProtocol.ManagmentModule:GetTrackedPlayer(W)
				--print("DEBUG Invite - Found in tracked players:", X and "yes" or "no")

				if X ~= nil then
					X.sendingInvite = false -- Сбрасываем флаг, так как приглашение прошло успешно
					updateNeeded = true
				end
			end

			if
				_:find("declines your group invitation.", 1, true)
				or _:find("отклоняет приглашение в группу.", 1, true)
			then
				-- Обновление статуса отказал приглашению в группу
				local n = string.sub(_, 1, _:find(" ") - 1)
				--print("DEBUG Decline - Original player name:", n)
				local W = AlphaProtocol.ManagmentModule:EnsureFullName(n):lower()
				--print("DEBUG Decline - Full player name:", W)
				local X = AlphaProtocol.ManagmentModule:GetTrackedPlayer(W)
				--print("DEBUG Decline - Found in tracked players:", X and "yes" or "no")
				if X and X.status ~= AlphaProtocol.ManagmentModule.strings.EXPIRED then
					X.status = AlphaProtocol.ManagmentModule.strings.DECLINED
					if X then
						X.declined = (X.declined or 0) + 1
						X.declinedTimestamps = X.declinedTimestamps or {}
						table.insert(X.declinedTimestamps, timeFormatted)
						--print("Отправка declined timestamp для игрока:", X.playerName)
						AlphaProtocol.OtherModule:SendSingleTimestamp(X.playerID, "declined", timeFormatted)
						EventsModule:CancelTimer(X.timer)
						X.status = AlphaProtocol.ManagmentModule.strings.DECLINED
						X.sendingInvite = false -- Сбрасываем флаг при отказе
						updateNeeded = true
					end
				end

				-- Игрок офлайн
			elseif _:find("Cannot find player", 1, true) or _:find("Игрок с именем", 1, true) then
				-- Извлекаем имя игрока из сообщения
				local playerName = _:match("Cannot find player '(.+)'%.")
					or _:match('Игрок с именем "(.+)" не найден%.')
				if playerName then
					local W = AlphaProtocol.ManagmentModule:EnsureFullName(playerName):lower()
					--print("DEBUG NOEXIST - Original message name:", playerName) -- Отладка
					--print("DEBUG NOEXIST - Extracted name with server:", W) -- Отладка
					for _, player in pairs(AlphaProtocol.ManagmentModule.playersToInvite) do
					--	print("DEBUG NOEXIST - Comparing with:", player.name:lower()) -- Отладка
					end
					local X = AlphaProtocol.ManagmentModule:GetTrackedPlayer(W)
					--print("DEBUG NOEXIST - Found player in list:", X and "yes" or "no") -- Отладка
					if X then
						X.noexist = (X.noexist or 0) + 1
						X.noexistTimestamps = X.noexistTimestamps or {}
						table.insert(X.noexistTimestamps, timeFormatted)
					--	print("Отправка noexist timestamp для игрока:", X.playerName)
						AlphaProtocol.OtherModule:SendSingleTimestamp(X.playerID, "noexist", timeFormatted)
						EventsModule:CancelTimer(X.timer)
						X.status = AlphaProtocol.ManagmentModule.strings.NOEXIST
						updateNeeded = true
					end
				end
			elseif
				_:find("is already in a group", 1, true) or _:find("уже состоит в группе.", 1, true)
			then
				-- Обработка сообщения об игроке уже в группе
				local n = string.sub(_, 1, _:find(" ") - 1)
				--print("DEBUG Busy - Original player name:", n)
				local W = AlphaProtocol.ManagmentModule:EnsureFullName(n):lower()
				--print("DEBUG Busy - Full player name:", W)
				local X = AlphaProtocol.ManagmentModule:GetTrackedPlayer(W)
				--print("DEBUG Busy - Found in tracked players:", X and "yes" or "no")
				if X then
					local a0 = EventsModule:TimeLeft(X.timer)
					if X and not AlphaProtocol.ManagmentModule:IsPlayerInRaid(n) and a0 > 58 then
						X.busy = (X.busy or 0) + 1
						X.busyTimestamps = X.busyTimestamps or {}
						table.insert(X.busyTimestamps, timeFormatted)
						--print("Отправка busy timestamp для игрока:", X.playerName)
						AlphaProtocol.OtherModule:SendSingleTimestamp(X.playerID, "busy", timeFormatted)
						EventsModule:CancelTimer(X.timer)
						X.status = AlphaProtocol.ManagmentModule.strings.BUSY
						updateNeeded = true
					end
				end
			end
			if
				_:find("left the raid group", 1, true)
				or _:find("покидает рейдовую группу.", 1, true)
			then
				--print("DEBUG: Caught raid leave event")
				local playerName = _:match("^(%S+)") -- Извлекаем имя до первого пробела
				--print("DEBUG: Player name extracted:", playerName)
				local W = AlphaProtocol.ManagmentModule:EnsureFullName(playerName):lower()
				--print("DEBUG: Full player name:", W)
				local X = AlphaProtocol.ManagmentModule:GetTrackedPlayer(W)
				if X then
					--print("DEBUG: Found player in tracked list")
					--print("DEBUG: Player Task:", X.Task)
					local raidKey = EventsModule:GetCurrentRaidName()
					--print("DEBUG: Current raid key:", raidKey)
					local raidData = EventsModule.raidList[raidKey]
					
					-- Проверяем выполнение задания
					local taskCompleted = EventsModule:hasPlayerCompletedHisTasks(X, raidData)
					--print("DEBUG: Task completed check result:", taskCompleted)
					
					if taskCompleted then
						--print("DEBUG: Player completed all tasks")
						X.status = AlphaProtocol.ManagmentModule.strings.DONE
						updateNeeded = true
					else
						--print("DEBUG: Player has not completed tasks")
						-- Получаем тип задания из TaskConfigModule
						local taskType = AlphaProtocol.TaskConfigModule:GetTaskType(X.Task)
						local killedCount = 0
						local totalCount = 0

						if taskType == "killLastBoss" then
							--print("DEBUG: Last boss task type")
							totalCount = 1
							local lastBossName
							
							-- Получаем конфигурацию рейда
							if AlphaProtocol.db.global.RaidConfigs2 then
								for _, config in pairs(AlphaProtocol.db.global.RaidConfigs2) do
									if config.name == raidKey and config.bosses and #config.bosses > 0 then
										lastBossName = config.bosses[#config.bosses].name
										--print("DEBUG: Found last boss name:", lastBossName)
										break
									end
								end
							end
							
							if lastBossName and X.bossKills then
								for _, kill in ipairs(X.bossKills) do
									if kill == lastBossName then
										killedCount = 1
										--print("DEBUG: Last boss was killed")
										break
									end
								end
							end
						elseif taskType == "killInfoBosses" then
						--	print("DEBUG: Info bosses task type")
							local bossesNeeded = {}
							for boss in X.info:gmatch("%b[]") do
								boss = boss:sub(2, -2)
								bossesNeeded[boss] = true
								totalCount = totalCount + 1
							end
							
							if X.bossKills then
								for boss, _ in pairs(bossesNeeded) do
									for _, kill in ipairs(X.bossKills) do
										if kill == boss then
											killedCount = killedCount + 1
											break
										end
									end
								end
							end
						elseif taskType == "killAllBosses" then
							--print("DEBUG: All bosses task type")
							local raidBosses = self:GetCurrentRaidBosses()
							if raidBosses then
								for bossName, _ in pairs(raidBosses) do
									totalCount = totalCount + 1
									if X.bossKills then
										for _, kill in ipairs(X.bossKills) do
											if kill == bossName then
												killedCount = killedCount + 1
												break
											end
										end
									end
								end
							end
						elseif taskType == "partialRaid" then
						--	print("DEBUG: Partial raid task type")
							-- Получаем требуемое количество боссов из задания
							local requiredBosses = AlphaProtocol.TaskConfigModule:GetPartialRaidBossCount(X.Task)
							if requiredBosses then
							--	print("DEBUG: Required bosses:", requiredBosses)
								totalCount = requiredBosses
								
								-- Получаем список боссов рейда в правильном порядке
								local orderedBosses = {}
								if AlphaProtocol.db.global.RaidConfigs2 then
									for _, config in pairs(AlphaProtocol.db.global.RaidConfigs2) do
										if config.name == raidKey and config.bosses then
											for i = 1, requiredBosses do
												if config.bosses[i] then
													orderedBosses[config.bosses[i].name] = i
												end
											end
											break
										end
									end
								end
								
								-- Проверяем убийства первых N боссов
								if X.bossKills then
									for _, kill in ipairs(X.bossKills) do
										if orderedBosses[kill] and orderedBosses[kill] <= requiredBosses then
											killedCount = killedCount + 1
										end
									end
								end
							--	print("DEBUG: Killed count:", killedCount, "out of", totalCount)
							end
						end
						
						print("Player " .. W .. " left the raid without completing tasks. Progress: " .. killedCount .. "/" .. totalCount)
						X.leave = (X.leave or 0) + 1
						X.leaveTimestamps = X.leaveTimestamps or {}
						table.insert(X.leaveTimestamps, timeFormatted)
						--print("Отправка leave timestamp для игрока:", X.playerName)
						AlphaProtocol.OtherModule:SendSingleTimestamp(X.playerID, "leave", timeFormatted)
						EventsModule:CancelTimer(X.timer)
						X.status = AlphaProtocol.ManagmentModule.strings.LEAVE
						updateNeeded = true
						if not AlphaProtocol.InterfaceModule.mainWindow:IsShown() then
							AlphaProtocol.InterfaceModule.mainWindow:Show()
						end
					end
				end
			end
		end
	end
	if updateNeeded then
		if AlphaProtocol.ManagmentModule then
			AlphaProtocol.ManagmentModule:UpdateMainWindow()
			-- Предполагается, что эта функция обновляет RaidDB на основе текущего состояния playersToInvite
			Main:SavePlayersToRaidDB()
		end
	end
end

EventsModule:RegisterEvent("GROUP_ROSTER_UPDATE", "OnEvent")
EventsModule:RegisterEvent("CHAT_MSG_SYSTEM", "OnEvent")
EventsModule:RegisterEvent("UI_ERROR_MESSAGE", "OnEvent")

-- Нужна для правильного серверного времени
function EventsModule:GetServerTimeFormatted()
	local hour, minute = GetGameTime() -- Получаем серверное игровое время
	-- Форматируем время в строку "ЧЧ:ММ"
	local formattedTime = string.format("%02d:%02d", hour, minute)
	return formattedTime
end

function EventsModule:CombinedEncounterEndHandler(...)
	self:OnEncounterEnd(...)
	self:ReminderAfterBossKill(...)
end

function EventsModule:OnEnable()
	self:RegisterEvent("PLAYER_TARGET_CHANGED", "ReminderForgotten")
	self:RegisterEvent("ENCOUNTER_END", "CombinedEncounterEndHandler")
end

---------------------------------------------- ДУБЛИРОВАТЬ НАЗВАНИЕ БОССА ОБЯЗАТЕЛЬНО ---------------------------------------------------
-- /run local t = {GetInstanceInfo()}; print(t[1], "=", t[8]) -- Получаем номер instanceMapID
-- Таблица с именами боссов для проверки ["Ил'гинот, Сердце Порчи"] = {"Ил'гинот, Сердце Порчи", "Глаз Ил'гинота", "Щупальце угнетения"},
EventsModule.raidList = {

}

-- Получение информации о рейде из базы данных или статического списка
function EventsModule:GetRaidInfo(raidName)
	-- Сначала проверяем в конфигурациях рейдов
	if AlphaProtocol.db.global.RaidConfigs2 then
		for _, raidConfig in pairs(AlphaProtocol.db.global.RaidConfigs2) do
			if raidConfig.name == raidName then
				local bossesTable = {}
				for _, boss in ipairs(raidConfig.bosses) do
					bossesTable[boss.name] = { boss.name }  -- Формат совместимый со старой системой
				end
				return {
					instanceMapID = raidConfig.mapID,
					bossCount = #raidConfig.bosses,
					bosses = bossesTable
				}
			end
		end
	end
	
	-- Если не нашли в базе данных, возвращаем из статического списка
	return self.raidList[raidName]
end

function EventsModule:GetCurrentRaidName()
	local instanceName, type, _, _, _, _, _, instanceMapID = GetInstanceInfo()
	--print("DEBUG GetCurrentRaidName [EN]: Instance info - Name:", instanceName, "Type:", type, "MapID:", instanceMapID)
	
	if type == "raid" or type == "party" then
		-- Пробуем получить ID инстанса через API Путеводителя
		local journalInstanceID = C_EncounterJournal.GetInstanceForGameMap(instanceMapID)
		--print("DEBUG GetCurrentRaidName [EN]: Journal Instance ID:", journalInstanceID)
		
		if journalInstanceID then
			-- Получаем информацию об инстансе из Путеводителя
			EJ_SelectInstance(journalInstanceID)
			local journalName = select(1, EJ_GetInstanceInfo(journalInstanceID))
			--print("DEBUG GetCurrentRaidName [EN]: Journal Instance Name:", journalName)
			
			-- Проверяем прямое совпадение в конфигурациях
			if AlphaProtocol.db.global.RaidConfigs2 then
				for _, config in pairs(AlphaProtocol.db.global.RaidConfigs2) do
					if config.name == journalName then
						--print("DEBUG GetCurrentRaidName [EN]: Direct match found in configs:", config.name)
						return config.name
					end
				end
			end
			
			-- Если не нашли в конфигурациях, возвращаем имя из журнала
			return journalName
		end
		
		-- Если не удалось получить ID через API, используем имя инстанса
		--print("DEBUG GetCurrentRaidName [EN]: Falling back to instance name:", instanceName)
		
		-- Проверяем прямое совпадение в конфигурациях
		if AlphaProtocol.db.global.RaidConfigs2 then
			for _, config in pairs(AlphaProtocol.db.global.RaidConfigs2) do
				if config.name == instanceName then
				--	print("DEBUG GetCurrentRaidName [EN]: Direct match found in configs:", config.name)
					return config.name
				end
			end
		end
		
		-- Если не нашли в конфигурациях, возвращаем имя инстанса
		return instanceName
	end
	
--	print("DEBUG GetCurrentRaidName [EN]: No match found, returning nil")
	return nil
end

-- Вспомогательная функция для поиска в конфигурациях
function EventsModule:FindRaidInConfigs(journalName)
	-- Проверяем сначала в конфигурациях рейдов
	if AlphaProtocol.db.global.RaidConfigs2 then
		for _, raidConfig in pairs(AlphaProtocol.db.global.RaidConfigs2) do
			if raidConfig.name == journalName then
				return raidConfig.name
			end
		end
	end
	
	-- Если не нашли в конфигурациях, проверяем в старом списке
	for key, _ in pairs(self.raidList) do
		if key == journalName then
			return key
		end
	end
	
	return nil
end

-- Получение списка боссов для текущего рейда
function EventsModule:GetCurrentRaidBosses()
	local raidName = self:GetCurrentRaidName()
	if not raidName then return nil end
	
	-- Проверяем сначала в конфигурациях рейдов
	if AlphaProtocol.db.global.RaidConfigs2 then
		for _, raidConfig in pairs(AlphaProtocol.db.global.RaidConfigs2) do
			if raidConfig.name == raidName then
				local bossesTable = {}
				for _, boss in ipairs(raidConfig.bosses) do
					bossesTable[boss.name] = boss  -- Сохраняем весь объект босса
				end
				return bossesTable
			end
		end
	end
	
	-- Если не нашли в конфигурациях, возвращаем из старого списка
	if self.raidList[raidName] then
		--print("old bosses")
		return self.raidList[raidName].bosses
	end
	
	return nil
end

-------------------------------------------------------------------------
-------------------- Подсчет убитых боссов ------------------------------
-------------------------------------------------------------------------
function EventsModule:OnEncounterEnd(event, encounterID, encounterName, difficultyID, groupSize, success)
--	print("DEBUG OnEncounterEnd: Started for boss:", encounterName)
	-- Определяем название рейда
	local raidKey = self:GetCurrentRaidName()
--	print("DEBUG OnEncounterEnd: Current raid:", raidKey)
	-- Преобразование успеха из числового значения в булево
	success = success == 1
--	print("DEBUG OnEncounterEnd: Fight success:", success)

	-- Проверяем, есть ли имя босса в списке bossList и был ли бой успешен
	local raidBossName
	local lowerEncounterName = encounterName:lower() -- Приведение к нижнему регистру для сравнения
	--print("DEBUG OnEncounterEnd: Looking for boss:", lowerEncounterName)

	-- Получаем список боссов текущего рейда
	local bossList = self:GetCurrentRaidBosses()
	if not bossList then
		--print("DEBUG OnEncounterEnd: No boss list found for raid")
		-- Если список боссов не найден, используем старый список для обратной совместимости
		if self.raidList[raidKey] and self.raidList[raidKey].bosses then
			bossList = self.raidList[raidKey].bosses
			--print("DEBUG OnEncounterEnd: Using old boss list")
		else
			--print("DEBUG OnEncounterEnd: No boss list available, exiting")
			return
		end
	end

	-- Поиск босса в списке
	for bossName, bossInfo in pairs(bossList) do
		--print("DEBUG OnEncounterEnd: Checking boss:", bossName)
		if type(bossInfo) == "table" and bossInfo.name then
			-- Новый формат
			if bossInfo.name:lower() == lowerEncounterName then
				raidBossName = bossInfo.name
				--print("DEBUG OnEncounterEnd: Found boss in new format:", raidBossName)
				break
			end
			-- Проверка альтернативных имен
			if bossInfo.altNames then
				for _, altName in ipairs(bossInfo.altNames) do
					if altName:lower() == lowerEncounterName then
						raidBossName = bossInfo.name  -- Используем основное имя босса
						--print("DEBUG OnEncounterEnd: Found boss by alt name:", altName, "->", raidBossName)
						break
					end
				end
				if raidBossName then break end  -- Если нашли по альтернативному имени, выходим из цикла
			end
		else
			-- Старый формат
			if bossName:lower() == lowerEncounterName then
				raidBossName = bossName
				--print("DEBUG OnEncounterEnd: Found boss in old format:", raidBossName)
				break
			end
		end
	end

	-- Добавляем отладочный вывод, если босс не найден
	if not raidBossName then
		print("DEBUG OnEncounterEnd: Boss not found in any format. Encounter name:", encounterName, "Lower case:", lowerEncounterName)
	end

	-- Если босс не из списка или бой был проигран, завершаем функцию
	if not raidBossName or not success then
		--print("DEBUG OnEncounterEnd: Boss not found or fight failed, exiting")
		return
	end

	local needToUpdateDB = false

	-- Сбор информации о текущих участниках рейда
	local raidMembers = {}
	for i = 1, MAX_RAID_MEMBERS do
		local name = GetRaidRosterInfo(i)
		if name then
			raidMembers[name:lower()] = true
		--	print("DEBUG OnEncounterEnd: Found raid member:", name:lower())
		end
	end

	-- Перебор списка игроков, предназначенных для участия в рейде
	--print("DEBUG OnEncounterEnd: Checking playersToInvite list")
	for _, playerInfo in ipairs(AlphaProtocol.ManagmentModule.playersToInvite) do
		local name, server = strsplit("-", playerInfo.name:lower())
		local fullName = name .. (server and "-" .. server or "")
		--print("DEBUG OnEncounterEnd: Checking player:", fullName)
		local presentInRaid = raidMembers[fullName] or raidMembers[name]
		--print("DEBUG OnEncounterEnd: Player present in raid:", presentInRaid)

		-- Проверка наличия игрока в рейде
		if presentInRaid then
			--print("DEBUG OnEncounterEnd: Processing player:", fullName)
			-- Если игрок находится в рейде, обновляем его данные
			if not playerInfo.bossKills then
				--print("DEBUG OnEncounterEnd: Creating new bossKills table for player")
				playerInfo.bossKills = {} -- Создание списка убийств боссов, если он еще не существует
			end
			-- Добавление имени босса в список убийств
			--print("DEBUG OnEncounterEnd: Adding boss kill:", raidBossName)
			table.insert(playerInfo.bossKills, raidBossName)
			needToUpdateDB = true
			--print("DEBUG OnEncounterEnd: Updated bossKills for player:", fullName, "Total kills:", #playerInfo.bossKills)
		end
	end

	-- Обновляем базу данных после обработки всех игроков, если это необходимо
	if needToUpdateDB then
		--print("DEBUG OnEncounterEnd: Saving changes to RaidDB")
		Main:SavePlayersToRaidDB()
		AlphaProtocol.InterfaceModule:UpdateMainWindowRaidInfo()-- Отмечаем изменение данных
	end
end
-------------------------------------------------------------------------
-------------------- Напоминает о забытых игроках -----------------------
-------------------------------------------------------------------------
-- Напоминаем после убийства босса
function EventsModule:ReminderAfterBossKill(event, encounterID, encounterName, difficultyID, groupSize, success)
	local raidKey = self:GetCurrentRaidName()
	if not raidKey or not IsInRaid() then return end
	
	if event == "ENCOUNTER_END" and success == 1 then
		local currentBossName = encounterName
		local raidConfig
		
		-- Получаем конфигурацию рейда сначала из новой БД
		if AlphaProtocol.db.global.RaidConfigs2 then
			for _, config in pairs(AlphaProtocol.db.global.RaidConfigs2) do
				if config.name == raidKey then
					raidConfig = config
					break
				end
			end
		end
		
		-- Если не нашли в новой БД, используем старый список
		if not raidConfig and self.raidList[raidKey] then
			raidConfig = self.raidList[raidKey]
		end
		
		if not raidConfig then return end
		
		-- Находим текущего босса и определяем следующего
		local currentBossIndex
		local nextBoss
		
		if raidConfig.bosses then
			-- Для нового формата (из БД)
			for i, boss in ipairs(raidConfig.bosses) do
				if boss.name:lower() == currentBossName:lower() then
					currentBossIndex = i
					if i < #raidConfig.bosses then
						nextBoss = raidConfig.bosses[i + 1]
					end
					break
				end
			end
			
			-- Если не нашли в новом формате, проверяем старый формат
			if not currentBossIndex and self.raidList[raidKey] then
				for bossName, mobNames in pairs(self.raidList[raidKey].bosses) do
					for i = 1, #mobNames do
						if mobNames[i]:lower() == currentBossName:lower() then
							for nextBossName, nextMobNames in pairs(self.raidList[raidKey].bosses) do
								if tonumber(nextMobNames[1]) == tonumber(mobNames[1]) + 1 then
									nextBoss = { name = nextBossName }
									break
								end
							end
							break
						end
					end
					if nextBoss then break end
				end
			end
		end
		
		if nextBoss then
			local playersToReminder = {}
			
			-- Проверяем всех игроков
			for _, playerInfo in ipairs(AlphaProtocol.ManagmentModule.playersToInvite) do
				local fullName = self:EnsureFullName(playerInfo.name)
				local needsBoss = false
				local bossAlreadyKilled = false
				
				-- Проверяем, не убил ли уже игрок этого босса
				for _, killedBossName in ipairs(playerInfo.bossKills or {}) do
					if killedBossName:lower() == nextBoss.name:lower() then
						bossAlreadyKilled = true
						break
					end
				end
				
				-- Если босс еще не убит, проверяем нужен ли он игроку
				if not bossAlreadyKilled then
					local taskType = AlphaProtocol.TaskConfigModule:GetTaskType(playerInfo.Task)

					if taskType == "killLastBoss" then
						local lastBossName
						if AlphaProtocol.db.global.RaidConfigs2 then
							for _, config in pairs(AlphaProtocol.db.global.RaidConfigs2) do
								if config.name == raidKey and config.bosses and #config.bosses > 0 then
									lastBossName = config.bosses[#config.bosses].name
									break
								end
							end
						end
						
						if lastBossName and lastBossName:lower() == nextBoss.name:lower() then
							needsBoss = true
						end
					elseif taskType == "killInfoBosses" then
						for item in string.gmatch(playerInfo.info, "%b[]") do
							item = item:sub(2, -2)
							if item:lower() == nextBoss.name:lower() then
								needsBoss = true
								break
							end
						end
					elseif taskType == "killAllBosses" then
						needsBoss = true
					end
				end
				
				-- Если босс нужен и игрок не в рейде, добавляем в список для напоминания
				if needsBoss and not self:IsPlayerInRaid(fullName) then
					if not playerInfo.isHide and playerInfo.status ~= AlphaProtocol.ManagmentModule.strings.OTHER_RAID then  -- Добавляем проверку OTHER_RAID
						table.insert(playersToReminder, { name = fullName, boss = nextBoss.name })
					end
				end
			end
			
			-- Показываем напоминания
			if #playersToReminder > 0 then
				print("Adding " .. #playersToReminder .. " players to reminder for boss " .. nextBoss.name .. ", showing alert in 8 seconds")
				C_Timer.After(8, function()
					AlphaProtocol.InterfaceModule:ShowBatchNotifications(playersToReminder)
				end)
			end
		end
	end
end

-- Напоминание после нажатия на цель
function EventsModule:ReminderForgotten()
	local raidKey = self:GetCurrentRaidName()
	--print("DEBUG ReminderForgotten [EN]: Current raid key:", raidKey)
	
	if not raidKey or not IsInRaid() or UnitAffectingCombat("player") then
		--print("DEBUG ReminderForgotten [EN]: Early return conditions - No raid key:", not raidKey, "Not in raid:", not IsInRaid(), "In combat:", UnitAffectingCombat("player"))
		return
	end

	local targetName = UnitName("target")
	--print("DEBUG ReminderForgotten [EN]: Target name:", targetName)
	
	if not targetName or UnitIsDead("target") then
		--print("DEBUG ReminderForgotten [EN]: Early return - No target or target is dead")
		return
	end

	local lowerTargetName = targetName:lower()
	local playersToReminder = {}
	
	-- Получаем конфигурацию рейда
	local raidConfig
	if AlphaProtocol.db.global.RaidConfigs2 then
		for _, config in pairs(AlphaProtocol.db.global.RaidConfigs2) do
			if config.name == raidKey then
				raidConfig = config
			--	print("DEBUG ReminderForgotten [EN]: Found raid config:", config.name)
				-- Выводим всех боссов для отладки
			--	print("DEBUG ReminderForgotten [EN]: Bosses in config:")
				for _, boss in ipairs(config.bosses) do
				--	print("  - Boss name:", boss.name)
					if boss.altNames then
						--print("    Alt names:")
						for _, altName in ipairs(boss.altNames) do
						--	print("      *", altName)
						end
					end
				end
				break
			end
		end
	end
	
	if not raidConfig and self.raidList[raidKey] then
		raidConfig = self.raidList[raidKey]
	--	print("DEBUG ReminderForgotten [EN]: Using old raid list config for:", raidKey)
	end
	
	if not raidConfig then 
	--	print("DEBUG ReminderForgotten [EN]: No raid config found for:", raidKey)
		return 
	end
	
	-- Функция для проверки совпадения имени босса
	local function checkBossMatch(bossData, targetName)
		--print("DEBUG ReminderForgotten [EN] - checkBossMatch: Checking boss:", bossData.name, "against target:", targetName)
		--print("DEBUG ReminderForgotten [EN] - checkBossMatch: Comparing", bossData.name:lower(), "with", targetName:lower())
		
		if bossData.name:lower() == targetName:lower() then
		--	print("DEBUG ReminderForgotten [EN] - checkBossMatch: Direct name match found")
			return true
		end
		
		if bossData.altNames then
			for _, altName in ipairs(bossData.altNames) do
			--	print("DEBUG ReminderForgotten [EN] - checkBossMatch: Checking alt name:", altName:lower(), "against", targetName:lower())
				if altName:lower() == targetName:lower() then
				--	print("DEBUG ReminderForgotten [EN] - checkBossMatch: Alt name match found")
					return true
				end
			end
		end
		
		if type(bossData) == "table" and bossData[1] then
			for _, mobName in ipairs(bossData) do
			--	print("DEBUG ReminderForgotten [EN] - checkBossMatch: Checking old system name:", mobName:lower(), "against", targetName:lower())
				if mobName:lower() == targetName:lower() then
				--	print("DEBUG ReminderForgotten [EN] - checkBossMatch: Old system match found")
					return true
				end
			end
		end
		return false
	end
	
	-- Проверяем боссов в обеих системах
	local function findMatchingBoss()
	--	print("DEBUG ReminderForgotten [EN] - findMatchingBoss: Starting boss search")
		if raidConfig.bosses then
			for _, boss in ipairs(raidConfig.bosses) do
			--	print("DEBUG ReminderForgotten [EN] - findMatchingBoss: Checking boss:", boss.name)
				if checkBossMatch(boss, lowerTargetName) then
				--	print("DEBUG ReminderForgotten [EN] - findMatchingBoss: Match found:", boss.name)
					return boss.name
				end
			end
		end
	--	print("DEBUG ReminderForgotten [EN] - findMatchingBoss: No matching boss found")
		return nil
	end
	
	local matchedBossName = findMatchingBoss()
	--print("DEBUG ReminderForgotten [EN]: Matched boss name:", matchedBossName)
	
	if matchedBossName then
		for _, playerInfo in ipairs(AlphaProtocol.ManagmentModule.playersToInvite) do
			local fullName = self:EnsureFullName(playerInfo.name)
			--print("DEBUG ReminderForgotten [EN]: Checking player:", fullName)
			local needsBoss = false
			local bossAlreadyKilled = false
			
			-- Проверяем, не убил ли уже игрок этого босса
			for _, killedBossName in ipairs(playerInfo.bossKills or {}) do
				if killedBossName:lower() == matchedBossName:lower() then
					bossAlreadyKilled = true
					--print("DEBUG ReminderForgotten [EN]: Player already killed this boss")
					break
				end
			end
			
			if not bossAlreadyKilled then
				local taskType = AlphaProtocol.TaskConfigModule:GetTaskType(playerInfo.Task)
				--print("DEBUG ReminderForgotten [EN]: Task type:", taskType)

				if taskType == "killLastBoss" then
					local lastBossName
					if AlphaProtocol.db.global.RaidConfigs2 then
						for _, config in pairs(AlphaProtocol.db.global.RaidConfigs2) do
							if config.name == raidKey and config.bosses and #config.bosses > 0 then
								lastBossName = config.bosses[#config.bosses].name
								--print("DEBUG ReminderForgotten [EN]: Last boss name:", lastBossName)
								break
							end
						end
					end
					
					if lastBossName and lastBossName:lower() == matchedBossName:lower() then
						needsBoss = true
						--print("DEBUG ReminderForgotten [EN]: Player needs this boss (Last boss task)")
					end
				elseif taskType == "killInfoBosses" then
				--	print("DEBUG ReminderForgotten [EN]: Info bosses in task:", playerInfo.info)
					for item in string.gmatch(playerInfo.info, "%b[]") do
						item = item:sub(2, -2)
					--	print("DEBUG ReminderForgotten [EN]: Comparing info boss:", item:lower(), "with", matchedBossName:lower())
						if item:lower() == matchedBossName:lower() then
							needsBoss = true
						--	print("DEBUG ReminderForgotten [EN]: Player needs this boss (Info bosses task)")
							break
						end
					end
				elseif taskType == "killAllBosses" then
					needsBoss = true
				--	print("DEBUG ReminderForgotten [EN]: Player needs this boss (All bosses task)")
				end
			end
			
			if needsBoss and not self:IsPlayerInRaid(fullName) then
				if not playerInfo.isHide and playerInfo.status ~= AlphaProtocol.ManagmentModule.strings.OTHER_RAID then  -- Добавляем проверку OTHER_RAID
				--	print("DEBUG ReminderForgotten [EN]: Adding player to reminder list:", fullName)
					table.insert(playersToReminder, { name = fullName, boss = matchedBossName })
				end
			end
		end
		
		--print("DEBUG ReminderForgotten [EN]: Total players to remind:", #playersToReminder)
		if #playersToReminder > 0 then
			AlphaProtocol.InterfaceModule:ShowBatchNotifications(playersToReminder)
		end
	end
end

function EventsModule:EnsureFullName(playerName)
	if not playerName:find("-") then
		playerName = playerName .. "-" .. select(2, UnitFullName("player"))
	end
	return playerName:lower() -- Возвращаем имя в нижнем регистре для унификации
end

function EventsModule:IsPlayerInRaid(fullName)
	for i = 1, MAX_RAID_MEMBERS do
		local name, _, _, _, _, _, _, _, _, _, _, _ = GetRaidRosterInfo(i)
		if name then
			name = self:EnsureFullName(name) -- Обеспечиваем полноту имени с сервером
			if name == fullName then
				return true
			end
		end
	end
	return false
end
-------------------------------------------------------------------------
------------------- Проверка выполнения задания игрока-------------------
-------------------------------------------------------------------------
function EventsModule:hasPlayerCompletedHisTasks(playerInfo, raidData)
	--print("DEBUG hasPlayerCompletedHisTasks: Starting check for player:", playerInfo.name)
	if not playerInfo or not playerInfo.Task then
		--print("DEBUG hasPlayerCompletedHisTasks: No player info or task")
		return false
	end

	-- Получаем тип задания из TaskConfigModule
	local taskType = AlphaProtocol.TaskConfigModule:GetTaskType(playerInfo.Task)
	--print("DEBUG hasPlayerCompletedHisTasks: Task type:", taskType)
	if not taskType then
		--print("DEBUG hasPlayerCompletedHisTasks: Unknown task type")
		return false
	end

	-- Проверяем убийства боссов
	local bossKills = playerInfo.bossKills or {}
	--print("DEBUG hasPlayerCompletedHisTasks: Boss kills count:", #bossKills)
	local currentRaid = self:GetCurrentRaidName()
	--print("DEBUG hasPlayerCompletedHisTasks: Current raid:", currentRaid)
	
	-- Функция для проверки убийства босса (учитывает различные варианты имен)
	local function isBossKilled(bossName)
		--print("DEBUG hasPlayerCompletedHisTasks: Checking if boss killed:", bossName)
		for _, kill in ipairs(bossKills) do
			--print("DEBUG hasPlayerCompletedHisTasks: Comparing with kill:", kill)
			-- Проверяем точное совпадение
			if kill == bossName then
			--	print("DEBUG hasPlayerCompletedHisTasks: Exact match found")
				return true
			end
		end
		--print("DEBUG hasPlayerCompletedHisTasks: Boss not killed")
		return false
	end

	local result = false
	-- Проверяем выполнение задания в зависимости от его типа
	if taskType == "killLastBoss" then
		--print("DEBUG hasPlayerCompletedHisTasks: Checking last boss task")
		local lastBossName
		-- Получаем конфигурацию рейда
		if AlphaProtocol.db.global.RaidConfigs2 then
			for _, config in pairs(AlphaProtocol.db.global.RaidConfigs2) do
				if config.name == currentRaid and config.bosses and #config.bosses > 0 then
					lastBossName = config.bosses[#config.bosses].name
				--	print("DEBUG hasPlayerCompletedHisTasks: Found last boss:", lastBossName)
					break
				end
			end
		end
		
		if lastBossName then
			result = isBossKilled(lastBossName)
		else
		--	print("DEBUG hasPlayerCompletedHisTasks: Last boss name not found")
		end

	elseif taskType == "killInfoBosses" then
	--print("DEBUG hasPlayerCompletedHisTasks: Checking info bosses task")
		result = true
		for boss in playerInfo.info:gmatch("%b[]") do
			boss = boss:sub(2, -2) -- Удаляем квадратные скобки
			--print("DEBUG hasPlayerCompletedHisTasks: Checking info boss:", boss)
			if not isBossKilled(boss) then
				result = false
				break
			end
		end

	elseif taskType == "killAllBosses" then
		--print("DEBUG hasPlayerCompletedHisTasks: Checking all bosses task")
		if not currentRaid then
			--print("DEBUG hasPlayerCompletedHisTasks: No current raid")
			return false
		end

		-- Получаем список боссов текущего рейда
		local raidBosses = self:GetCurrentRaidBosses()
		if not raidBosses then
		--	print("DEBUG hasPlayerCompletedHisTasks: No raid bosses list")
			return false
		end

		result = true
		for bossName, _ in pairs(raidBosses) do
			--print("DEBUG hasPlayerCompletedHisTasks: Checking raid boss:", bossName)
			if not isBossKilled(bossName) then
				result = false
				break
			end
		end
	elseif taskType == "partialRaid" then
		--print("DEBUG: Partial raid task type")
		-- Получаем требуемое количество боссов из задания
		local requiredBosses = AlphaProtocol.TaskConfigModule:GetPartialRaidBossCount(playerInfo.Task)
		if requiredBosses then
		--	print("DEBUG: Required bosses:", requiredBosses)
			totalCount = requiredBosses
			
			-- Получаем список боссов рейда в правильном порядке
			local orderedBosses = {}
			if AlphaProtocol.db.global.RaidConfigs2 then
				for _, config in pairs(AlphaProtocol.db.global.RaidConfigs2) do
					if config.name == currentRaid and config.bosses then
						for i = 1, requiredBosses do
							if config.bosses[i] then
								orderedBosses[config.bosses[i].name] = i
							end
						end
						break
					end
				end
			end
			
			-- Проверяем убийства первых N боссов
			local killedCount = 0
			if playerInfo.bossKills then
				for _, kill in ipairs(playerInfo.bossKills) do
					if orderedBosses[kill] and orderedBosses[kill] <= requiredBosses then
						killedCount = killedCount + 1
					end
				end
			end
			--print("DEBUG: Killed count:", killedCount, "out of", totalCount)
			result = (killedCount >= totalCount)
		end
	end

	--print("DEBUG hasPlayerCompletedHisTasks: Final result:", result)
	return result
end

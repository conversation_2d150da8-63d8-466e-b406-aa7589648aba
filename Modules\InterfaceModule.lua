local AlphaProtocol = LibStub("AceAddon-3.0"):GetAddon("FriendshipIsMagic")
local InterfaceModule = AlphaProtocol:NewModule("InterfaceModule", "AceConsole-3.0", "AceEvent-3.0")
local Main = AlphaProtocol:GetModule("Main")
local StdUi = LibStub("StdUi"):NewInstance()
local LDB = LibStub("LibDataBroker-1.1")
local LDBIcon = LibStub("LibDBIcon-1.0")
local LibEasyMenu = LibStub:GetLibrary("LibEasyMenu")
-- Добавляем переменную для хранения времени последней синхронизации
InterfaceModule.lastSyncTime = nil

function InterfaceModule:OnEnable()
	-- Сначала генерируем текущий пароль
	self:GeneratePassword()

	-- Проверяем сохраненный пароль на валидность
	local storedHash = Main.db.global.i
	if storedHash then
		local currentHash = self:lolcheckPassword()
		if not currentHash or currentHash == 6 then
			-- Если пароль больше не действителен, очищаем его
			Main.db.global.i = nil
			Main.passwordChecked = false
			print("Пароль устарел. Пожалуйста, введите новый пароль для текущей недели.")
			return
		end
	end

	Main.passwordChecked = Main.db.global.i or false
	
	-- Загружаем время последней синхронизации, если оно есть
	if Main.db.profile.lastSyncTime then
		self.lastSyncTime = Main.db.profile.lastSyncTime
	end
	
	if Main.passwordChecked then
		self:InitializeInterface()
	end
end

function InterfaceModule:InitializeInterface()
	-- Проверяем и обновляем пароль

	
	-- Инициализируем интерфейс
	self:MenuButton()
	self:ToggleMainMenuButton()
	self:DrawMinimapButton()
	
	-- Отображаем время последней синхронизации
	self:DisplayLastSyncTime()
end

function InterfaceModule:CheckPassword(inputPassword)
	local res = self:lolcheckPassword(inputPassword)
	if not res then
		print("Неподходящий пароль.")
	elseif res == 6 then
		print("Пароль для следующей недели сохранен.")
		Main.passwordChecked = false
	else
		Main.passwordChecked = true
		self:InitializeInterface()
		Main:InitializeAfterPassword()
	end
end

-------------------------------------------------------------------------
-------------------- Универсальные функции ------------------------------
-------------------------------------------------------------------------
-- Стандартный уровень
InterfaceModule.FRAME_LEVEL = 0

-- Последующие уровни фреймов. Пример вызова: mainWindow:SetFrameLevel(self:GetNextFrameLevel())
function InterfaceModule:GetNextFrameLevel()
	-- Сбрасываем уровень, если он достиг максимума
	if self.FRAME_LEVEL >= 9000 then
		self.FRAME_LEVEL = 0
	end
	self.FRAME_LEVEL = self.FRAME_LEVEL + 100
	--print(self.FRAME_LEVEL)
	return self.FRAME_LEVEL
end

-- Проверка, что фрейм создан
-- frameRef строковый идентификатор/createFunc имя метода, который отвечает за создание фрейма.
-- Простой пример в InterfaceModule:MenuButton()
function InterfaceModule:EnsureFrameCreated(frameRef, createFunc)
	if not self[frameRef] then
		self[createFunc](self)
	end
end

-- Показать/Скрыть фрейм
-- Простой пример в InterfaceModule:MenuButton()
function InterfaceModule:ToggleFrameVisibility(frame)
	if frame and frame:IsShown() then
		frame:Hide()
	elseif frame then
		-- При отображении фрейма устанавливаем ему новый максимальный уровень
		-- Для специальных окон синхронизируем уровень с mainWindow
		if frame == self.optionalWindow or frame == self.sidebarWindow or frame == self.hiddenPlayersWindow then
			if self.mainWindow then
				frame:SetFrameLevel(self.mainWindow:GetFrameLevel())
			end
		else
			frame:SetFrameLevel(self:GetNextFrameLevel())
		end
		frame:Show()
	end
end

-- Необратимая хэш-функция (принимает строку - возвращает число)
function InterfaceModule:irr_hash(input)
	local hash = 0
	local prime1 = 5381
	local prime2 = 3356
	local prime3 = 2 ^ 16 + 1

	for i = 1, #input do
		local char = string.byte(input, i)
		hash = ((hash * prime2) % prime3 + char) % prime3
		hash = ((hash * prime1) % prime3 + char) % prime3
	end

	return hash
end



function InterfaceModule:getWeekNumber()
    local calendarTime = C_DateAndTime.GetCurrentCalendarTime()
    

    
    -- Конвертируем календарное время в timestamp
    local calendarTimestamp = time({
        year = calendarTime.year,
        month = calendarTime.month,
        day = calendarTime.monthDay,
        hour = calendarTime.hour,
        min = calendarTime.minute,
        sec = calendarTime.second
    })
    
    local weeks = math.floor((calendarTimestamp - 511200) / 604800)
    return weeks
end

function InterfaceModule:getPassword(weekNumber, name)
	local letters = {}
	local hash = self:irr_hash("recurrent" .. weekNumber .. name .. weekNumber - 1200)

	local len = #name
	local hashLen = #tostring(hash * 2)

	for i = 1, 5 do
		local stepSize = (i * (weekNumber % 5 + 1)) % hashLen
		local startIndex = stepSize % hashLen + 1
		local endIndex = math.min(startIndex + 1, hashLen)
		local index = tonumber(tostring(hash * 2):sub(startIndex, endIndex), 16) % len + 1
		table.insert(letters, name:sub(index, index))
	end

	print(table.concat(letters) .. self:irr_hash(tostring(weekNumber * 42 + 3156)))
	return self:irr_hash(table.concat(letters) .. self:irr_hash(tostring(weekNumber * 42 + 3156)))
end

function InterfaceModule:GeneratePassword()
	local name = "{RLSUB}"

	local weekNumber = self:getWeekNumber()

	local hashedPassword = self:getPassword(weekNumber, name)
	local nextHashedPassword = self:getPassword(weekNumber + 1, name)
	return hashedPassword, nextHashedPassword
end

function InterfaceModule:lolcheckPassword(pass)
	local curent, next = self:GeneratePassword()

	if pass then
		pass = self:irr_hash(pass)
		if curent == pass then
			Main.db.global.i = pass
			return 5
		elseif next == pass then
			Main.db.global.nexti = pass
			return 6
		else
			return false
		end
	end
	if Main.db.global.nexti == curent then
		Main.db.global.i = Main.db.global.nexti
		return true
	elseif Main.db.global.i == curent then
		return true
	end
	return false
end

-------------------------------------------------------------------------
-------------------- Главное меню ---------------------------------------
-------------------------------------------------------------------------
-- Нужна для настроек скрытия/отображения
function InterfaceModule:ToggleMainMenuButton()
	if self.mainButton then
		if Main.db.profile.showMainMenuButton then
			self.mainButton:Show()
		else
			self.mainButton:Hide()
		end
	end
end

-- Кнопка главного меню
function InterfaceModule:MenuButton()
	-- Создание кнопки
	if not self.mainButton then
		-- Заменяем обычную кнопку на кнопку с подсветкой
		self.mainButton = StdUi:HighlightButton(UIParent, 120, 30)
		
		-- Устанавливаем текстуры для разных состояний кнопки
		self.mainButton:SetNormalTexture("Interface\\AddOns\\FriendshipIsMagic\\Assets\\icon.tga")
		self.mainButton:SetPushedTexture("Interface\\AddOns\\FriendshipIsMagic\\Assets\\icon.tga")
		self.mainButton:SetHighlightTexture("Interface\\AddOns\\FriendshipIsMagic\\Assets\\icon.tga", "ADD")
		
		-- Проверка наличия сохраненной позиции
		local pos = Main.db.profile.mainButtonPosition
		if pos then
			self.mainButton:SetPoint(pos.point, UIParent, pos.relativePoint, pos.xOfs, pos.yOfs)
		else
			-- Позиция по умолчанию, если сохраненная позиция не найдена
			self.mainButton:SetPoint("TOP", 0, 0)
		end
		
		-- Инициализируем текст, если есть сохраненное время
		if self.lastSyncTime then
			self:DisplayLastSyncTime()
		end
	end

	-- Нажатие на кнопку
	self.mainButton:SetScript("OnClick", function()
		-- Проверка, что фрейм создан
		self:EnsureFrameCreated("menuFrame", "CreateMenu")
		-- Показать/Скрыть фрейм
		self:ToggleFrameVisibility(self.menuFrame)
	end)
end

-- Фрейм главного меню
function InterfaceModule:CreateMenu()
	local menuFrame = StdUi:Frame(UIParent, 120, 120)
	menuFrame:SetPoint("TOP", self.mainButton, "BOTTOM", 0, 0)
	menuFrame:Hide()

	local button1 = StdUi:Button(menuFrame, 120, 20, "Main Window")
	button1:SetPoint("TOP", self.mainButton, "BOTTOM", 0, -5)
	button1:SetScript("OnClick", function()
		self:EnsureFrameCreated("mainWindow", "CreateMainWindow")
		self:ToggleFrameVisibility(self.mainWindow)
	end)

	local button2 = StdUi:Button(menuFrame, 120, 20, "Raid History")
	button2:SetPoint("TOP", button1, "BOTTOM", 0, -5)
	button2:SetScript("OnClick", function()
		self:EnsureFrameCreated("raidHistory", "CreateRaidHistory")
		self:ToggleFrameVisibility(self.raidHistory)
	end)

	local button3 = StdUi:Button(menuFrame, 120, 20, "Loot History")
	button3:SetPoint("TOP", button2, "BOTTOM", 0, -5)
	button3:SetScript("OnClick", function()
		-- 1. Получаем или создаем окно лута
		local lootWindow = AlphaProtocol.LootModule:GetOrCreateLootWindow()
		-- 2. Если окно будет показано (т.е. сейчас оно скрыто), обновляем его данные
		if not lootWindow:IsShown() then
			AlphaProtocol.LootModule:RefreshLootData() -- Обновляем общие данные
			-- Обновляем список в таблице окна
			if lootWindow.UpdateLootList then 
				lootWindow:UpdateLootList(lootWindow.bossDropdown:GetValue(), lootWindow.searchBox:GetText())
			end
		end
		-- 3. Используем стандартную функцию для показа/скрытия
		self:ToggleFrameVisibility(lootWindow)
	end)

	self.menuFrame = menuFrame
end

-- Позволяет двигать кнопку главного меню
function InterfaceModule:UpdateMenuButtonMovableState()
	if self.mainButton then
		-- Всегда разрешаем взаимодействие с кнопкой
		self.mainButton:EnableMouse(true)

		if Main.tempSettings.allowMoveMainMenuButton then
			-- Включаем возможность перемещения
			self.mainButton:SetMovable(true)
			self.mainButton:RegisterForDrag("LeftButton")
			self.mainButton:SetScript("OnDragStart", self.mainButton.StartMoving)
			self.mainButton:SetScript("OnDragStop", function()
				self.mainButton:StopMovingOrSizing()
				local point, _, relativePoint, xOfs, yOfs = self.mainButton:GetPoint()
				Main.db.profile.mainButtonPosition = {
					point = point,
					relativePoint = relativePoint,
					xOfs = xOfs,
					yOfs = yOfs,
				}
			end)
		else
			-- Выключаем возможность перемещения
			self.mainButton:SetMovable(false)
			self.mainButton:RegisterForDrag()
			self.mainButton:SetScript("OnDragStart", nil)
			self.mainButton:SetScript("OnDragStop", nil)
		end
	end
end
-------------------------------------------------------------------------
-------------------- Главное окно рейда ---------------------------------
-------------------------------------------------------------------------
function InterfaceModule:CreateMainWindow()
	-- Проверяем, инициализированы ли все необходимые модули
	if not AlphaProtocol.ManagmentModule then
		if not Main.passwordChecked then
			print("В доступе отказано.")
			return
		end
		Main:InitializeAfterPassword()
	end

	-- Проверяем пароль
	local generatedHash = self:lolcheckPassword()
	if not generatedHash or generatedHash == 6 then
	--	print("В доступе отказано.")
		return
	end

	-- Если окно уже существует, просто показываем его
	if self.mainWindow then
		self.mainWindow:Show()
		return
	end

	local mainWindow = StdUi:Window(UIParent, 750, 400)
	mainWindow:SetPoint("CENTER")
	mainWindow:SetFrameLevel(self:GetNextFrameLevel())
	StdUi:MakeResizable(mainWindow, "BOTTOMRIGHT")
	-- Важно! Если не добавить, тогда после /reload или входа в игру придется нажимать 2 раза на кнопку вызова главного окна.
	mainWindow:Hide()
	mainWindow.closeBtn:Hide()

	self:EnableEscapeKey(mainWindow)

	-- Проверка и применение сохраненных размеров окна
	if Main.db and Main.db.profile and Main.db.profile.windowSize then
		local savedSize = Main.db.profile.windowSize
		mainWindow:SetSize(savedSize.width, savedSize.height)
	end

	-- Установка сохраненной позиции окна, если она существует
	if Main.db and Main.db.profile and Main.db.profile.windowPosition then
		local pos = Main.db.profile.windowPosition
		mainWindow:ClearAllPoints() -- Очистка предыдущих точек привязки перед установкой новых
		mainWindow:SetPoint(pos.point, UIParent, pos.relativePoint, pos.xOfs, pos.yOfs)
	end

	-- Обработчик обновления уровня фрейма
	mainWindow:SetScript("OnMouseDown", function(self)
		self:SetFrameLevel(InterfaceModule:GetNextFrameLevel())
		InterfaceModule:SyncFrameLevels()
	end)

	-- Функция создания контекстного меню
	local function createContextMenu(playerName)
		local menu = {
			{
				text = "Hide",
				func = function()
					AlphaProtocol.ManagmentModule:HidePlayers(playerName)
				end,
			},
			{
				text = "Reveal all",
				func = function()
					AlphaProtocol.ManagmentModule:RevealAllPlayers()
				end,
			},
		}
		local menuFrame = CreateFrame("Frame", "ExampleMenuFrame", UIParent, "UIDropDownMenuTemplate")
		LibEasyMenu:EasyMenu(menu, menuFrame, "cursor", 0, 0, "MENU")
	end

	-- Создание колонок
	local tableColumns = {
		{
			name = "",
			width = 32,
			align = "CENTER",
			index = "countNumber",
			sortable = false
		},
		{
			name = "",
			width = 32,
			align = "CENTER",
			index = "icon",
			format = "icon",
			texture = true,
			sortable = false,
			events = {
				OnClick = function(_, cellFrame, _, rowData, _, _, _, _, _, ...)
					local buttonClicked = GetMouseButtonClicked()
					if buttonClicked == "LeftButton" then
						if rowData.icon == [=[Interface\RAIDFRAME\ReadyCheck-NotReady]=] then
							-- Нажатие на крестик - приглашение в группу
							AlphaProtocol.ManagmentModule:HandleInviteClick(rowData.name)
						elseif rowData.icon == [=[Interface\RAIDFRAME\ReadyCheck-Ready]=] then
							-- Нажатие на галочку - запрос на кик из группы
							StaticPopup_Show("CONFIRM_KICK_FROM_GROUP", rowData.name, nil, {
								playerName = rowData.name,
								onAccept = function()
									AlphaProtocol.ManagmentModule:HandleKickClick(rowData.name)
								end,
							})
						end
					else
						createContextMenu(rowData.name)
					end
				end,
			},
		},
		{
			name = "Status",
			width = 50,
			align = "CENTER",
			index = "status",
			format = "string",
			sortable = false,
			events = {
				OnClick = function(_, cellFrame, _, rowData, _, _, _, _, _, ...)
					local buttonClicked = GetMouseButtonClicked()
					if buttonClicked == "LeftButton" then
						return
					else
						createContextMenu(rowData.name)
					end
				end,
			},
		},
		{
			name = "",
			width = 32,
			align = "CENTER",
			index = "messageTexture",
			format = "icon",
			texture = true,
			sortable = false,
			events = {
				OnClick = function(_, cellFrame, _, rowData, _, _, _, _, _, ...)
					local buttonClicked = GetMouseButtonClicked()
					if buttonClicked == "LeftButton" then
						AlphaProtocol.ManagmentModule:HandleSendMessageButtonClick(rowData)
					else
						createContextMenu(rowData.name)
					end
				end,
			},
		},
		{
			name = "Name",
			width = 90,
			align = "CENTER",
			index = "displayName",
			format = "string",
			sortable = false,
			events = {
				OnClick = function(_, cellFrame, _, rowData, _, _, _, _, _, ...)
					local buttonClicked = GetMouseButtonClicked()
					if buttonClicked == "LeftButton" then
						local playerName = rowData.name
						local playerID = rowData.playerID
						local raidID = AlphaProtocol.ManagmentModule.playersToInvite
						InterfaceModule:SetupEditBoxForPlayerInfo(playerID, playerName, raidID, cellFrame, rowData)
					else
						createContextMenu(rowData.name)
					end
				end,
			},
		},
		{
			name = "Pilot",
			width = 50,
			align = "CENTER",
			index = "timeLeft",
			format = "string",
			sortable = false,
			events = {
				OnClick = function(_, cellFrame, _, rowData, _, _, _, _, _, ...)
					local buttonClicked = GetMouseButtonClicked()
					if buttonClicked == "LeftButton" then
						return
					else
						createContextMenu(rowData.name)
					end
				end,
				OnEnter = function(_, _, cell, data, _, _, _, _, _, ...)
					AlphaProtocol.ManagmentModule:CreatePilotTooltip(cell, data)
				end,
				OnLeave = function(_, _, _, _, _, _, _, _, _, ...)
					AlphaProtocol.ManagmentModule:HideInfoTooltip()
				end,
			},
		},
		{
			name = "Task",
			width = 130,
			align = "CENTER",
			index = "Task",
			format = function(value)
				if not value then return "" end
				-- Проверяем существование модуля и метода
				if not AlphaProtocol.TaskConfigModule or not AlphaProtocol.TaskConfigModule.GetTaskType then
					return value
				end
				-- Проверяем существование таска в конфигурации
				local success, taskType = pcall(function() 
					return AlphaProtocol.TaskConfigModule:GetTaskType(value)
				end)
				if not success or not taskType then
					-- Если тип задания не найден, возвращаем текст красного цвета
					return string.format("|cFFFF9999%s|r", value)
				end
				-- Если тип задания найден, возвращаем обычный текст
				return value
			end,
			sortable = false,
			events = {
				OnClick = function(_, cellFrame, _, rowData, _, _, _, _, _, ...)
					local buttonClicked = GetMouseButtonClicked()
					if buttonClicked == "LeftButton" then
						return
					else
						createContextMenu(rowData.name)
					end
				end,
				OnEnter = function(_, _, cell, data, _, _, _, _, _, ...)
					AlphaProtocol.ManagmentModule:CreateTaskTooltip(cell, data)
				end,
				OnLeave = function(_, _, _, _, _, _, _, _, _, ...)
					AlphaProtocol.ManagmentModule:HideInfoTooltip()
				end,
			},
		},
		{
			name = "Operator Info",
			width = 150,
			align = "CENTER",
			index = "info",
			format = "string",
			sortable = false,
			events = {
				OnEnter = function(_, _, cell, data, _, _, _, _, _, ...)
					AlphaProtocol.ManagmentModule:CreateInfoTooltip(cell, data)
				end,
				OnLeave = function(_, _, _, _, _, _, _, _, _, ...)
					AlphaProtocol.ManagmentModule:HideInfoTooltip()
				end,
				OnClick = function(_, cellFrame, _, rowData, _, _, _, _, _, ...)
					local buttonClicked = GetMouseButtonClicked()
					if buttonClicked == "LeftButton" then
						local playerName = rowData.name
						local playerID = rowData.playerID
						local raidID = AlphaProtocol.ManagmentModule.playersToInvite
						InterfaceModule:SetupEditBoxForOperatorInfo(playerID, playerName, raidID, cellFrame, rowData)
					else
						createContextMenu(rowData.name)
					end
				end,
			},
		},
		{
			name = "Raid Leader Info",
			width = 150,
			align = "CENTER",
			index = "rlinfo",
			format = "string",
			sortable = false,
			events = {
				OnEnter = function(_, _, cell, data, _, _, _, _, _, ...)
					AlphaProtocol.ManagmentModule:CreateInfoTooltipRL(cell, data)
				end,
				OnLeave = function(_, _, _, _, _, _, _, _, _, ...)
					AlphaProtocol.ManagmentModule:HideInfoTooltip()
				end,
				OnClick = function(_, cellFrame, _, rowData, _, _, _, _, _, ...)
					local buttonClicked = GetMouseButtonClicked()
					if buttonClicked == "LeftButton" then
						AlphaProtocol.ManagmentModule:HandleRowClick(cellFrame, rowData)
					else
						createContextMenu(rowData.name)
					end
				end,
			},
		},
	}
	-- Добавление статического всплывающего окна для подтверждения
	StaticPopupDialogs["CONFIRM_KICK_FROM_GROUP"] = {
		text = "Are you sure you want to kick %s from the group?",
		button1 = "Yes",
		button2 = "No",
		OnAccept = function(self, data)
			data.onAccept()
		end,
		timeout = 0,
		whileDead = true,
		hideOnEscape = true,
		showAlert = true,
	}

	-- Ограничивает изменение размера таблицы
	mainWindow:SetScript("OnSizeChanged", function(self, width, height)
		local minWidth, minHeight = 750, 300
		local maxWidth, maxHeight = 1200, 700

		if width < minWidth then
			self:SetWidth(minWidth)
		end
		if height < minHeight then
			self:SetHeight(minHeight)
		end
		if width > maxWidth then
			self:SetWidth(maxWidth)
		end
		if height > maxHeight then
			self:SetHeight(maxHeight)
		end

		-- Сохраняем размеры в базу данных
		Main.db.profile.windowSize = {
			width = self:GetWidth(),
			height = self:GetHeight(),
		}

		-- При изменении размера mainWindow, изменяем высоту sidebarWindow соответственно
		if InterfaceModule.sidebarWindow then
			InterfaceModule.sidebarWindow:SetHeight(height)
		end

		-- При изменении размера mainWindow, изменяем ширину optionalWindow соответственно
		if InterfaceModule.optionalWindow then
			InterfaceModule.optionalWindow:SetWidth(width)
		end
	end)

	local function customSortFunction(self, rowA, rowB, sortBy)
		local a = self:GetRow(rowA)
		local b = self:GetRow(rowB)

		-- Можно игнорировать sortBy, поскольку сортировка всегда будет по 'name'
		local idx = "name"
		local direction = "asc" -- Установите направление сортировки по умолчанию

		-- Функция для сравнения, поддерживающая разные типы данных
		local function compareValues(val1, val2)
			if type(val1) == "string" and type(val2) == "string" then
				return (direction == "asc" and val1 < val2) or (direction == "desc" and val1 > val2)
			end
			return false
		end

		-- Сортировка только по 'name'
		if a[idx] and b[idx] and a[idx] ~= b[idx] then
			return compareValues(a[idx], b[idx])
		end

		-- По умолчанию сохраняем текущий порядок
		return false
	end

	-- Созадет таблицу внутри основного окна
	mainWindow.playerTable = StdUi:ScrollTable(mainWindow, tableColumns, 16, 16)

	mainWindow.playerTable:SetDisplayRows(
		math.floor(mainWindow.playerTable:GetWidth() / mainWindow.playerTable:GetHeight()),
		mainWindow.playerTable.rowHeight
	)
	mainWindow.playerTable:SetPoint("TOPLEFT", mainWindow, "TOPLEFT", 10, -50)
	mainWindow.playerTable:SetPoint("BOTTOMRIGHT", mainWindow, "BOTTOMRIGHT", -10, 10)
	mainWindow.playerTable.CompareSort = customSortFunction

	-- Отключаем обработчики событий для заголовков
	mainWindow.playerTable.headerEvents = {}
	if mainWindow.playerTable.head then
		for _, columnFrame in ipairs(mainWindow.playerTable.head.columns) do
			columnFrame:SetScript("OnClick", nil)
			columnFrame:EnableMouse(false)
			if columnFrame.arrow then
				columnFrame.arrow:Hide()
			end
		end
	end

	mainWindow:SetScript("OnDragStop", function(self)
		self:StopMovingOrSizing()

		-- Получаем и сохраняем текущие координаты окна
		local point, _, relativePoint, xOfs, yOfs = self:GetPoint()
		Main.db.profile.windowPosition = {
			point = point,
			relativePoint = relativePoint,
			xOfs = xOfs,
			yOfs = yOfs,
		}
	end)

	-- Функция изменения размера таблицы
	mainWindow.playerTable:SetScript("OnSizeChanged", function(self)
		-- Получаем текущий размер таблицы
		local width = self:GetWidth()
		local height = self:GetHeight()

		-- Вычисляем сумму ширин всех столбцов
		local totalWidth = 0
		for column = 1, #self.columns do
			totalWidth = totalWidth + self.columns[column].width
		end

		-- Устанавливаем новую ширину для каждого столбца пропорционально изменению размера таблицы
		for column = 1, #self.columns do
			self.columns[column]:SetWidth(self.columns[column].width / totalWidth * (width - 30))
		end

		-- Устанавливаем количество отображаемых строк
		self:SetDisplayRows(math.floor(height / self.rowHeight), self.rowHeight)
	end)

	-- Создание/Открытие/Скрытие боковой панели
	local sidebarButton = StdUi:HighlightButton(mainWindow, 30, 30)
	sidebarButton:SetPoint("CENTER", mainWindow, "TOPLEFT", 20, -20)
	-- Устанавливаем текстуру для нормального состояния
	sidebarButton:SetNormalTexture("Interface\\AddOns\\FriendshipIsMagic\\Assets\\logo")
	-- Устанавливаем текстуру для нажатого состояния
	sidebarButton:SetPushedTexture("Interface\\AddOns\\FriendshipIsMagic\\Assets\\logo")
	-- Устанавливаем текстуру для подсветки
	sidebarButton:SetHighlightTexture("Interface\\AddOns\\FriendshipIsMagic\\Assets\\logo", "ADD")

	sidebarButton:SetScript("OnClick", function()
		InterfaceModule:EnsureFrameCreated("sidebarWindow", "SidebarWindow")
		InterfaceModule:ToggleFrameVisibility(self.sidebarWindow)
	end)

	-- Кастомная кнопка закрытия окна
	local customCloseBtn = StdUi:HighlightButton(mainWindow, 25, 25)
	customCloseBtn:SetPoint("CENTER", mainWindow, "TOPRIGHT", -20, -20)

	-- Устанавливаем пользовательские текстуры
	customCloseBtn:SetNormalTexture("Interface\\Buttons\\UI-Panel-MinimizeButton-Up")
	customCloseBtn:SetPushedTexture("Interface\\Buttons\\UI-Panel-MinimizeButton-Down")
	customCloseBtn:SetHighlightTexture("Interface\\Buttons\\UI-Panel-MinimizeButton-Highlight", "ADD")

	-- Скрыть главное окно
	customCloseBtn:SetScript("OnClick", function()
		mainWindow:Hide()
		-- Закрываем EditBox, если он существует и видим
		if InterfaceModule.playerNameEditBox and InterfaceModule.playerNameEditBox:IsShown() then
			InterfaceModule.playerNameEditBox:Hide()
		end
	end)

	-- Кнопка верхнего меню
	local optionalButton = StdUi:HighlightButton(mainWindow, 25, 25)
	optionalButton:SetPoint("CENTER", mainWindow, "TOPRIGHT", -40, -20)

	-- Устанавливаем текстуры для разных состояний кнопки
	optionalButton:SetNormalTexture("Interface\\Common\\help-i")
	optionalButton:SetPushedTexture("Interface\\Buttons\\UI-Quickslot-Depress")
	optionalButton:SetHighlightTexture("Interface\\Minimap\\UI-Minimap-ZoomButton-Highlight", "ADD")

	optionalButton:SetScript("OnClick", function()
		InterfaceModule:EnsureFrameCreated("optionalWindow", "OptionalWindow")
		InterfaceModule:ToggleFrameVisibility(self.optionalWindow)
	end)

	local labelText = mainWindow:CreateFontString(nil, "OVERLAY", "GameFontNormal")
	labelText:SetPoint("TOPLEFT", mainWindow, "TOPLEFT", 15, -38)
	labelText:SetText("0/0")

	--[[
	local raidLeaderIcon = mainWindow:CreateTexture(nil, "OVERLAY")
	raidLeaderIcon:SetPoint("TOPLEFT", mainWindow, "TOPLEFT", 5, -50) -- Смещение относительно текста
	raidLeaderIcon:SetSize(30, 30) -- Установка размеров картинки
	raidLeaderIcon:SetTexture("Interface\\AddOns\\FriendshipIsMagic\\Assets\\logo")
]]
	-- Добавление текстового поля для ввода фильтра
	local searchBox = StdUi:EditBox(mainWindow, 180, 24, "")
	searchBox:SetPoint("TOPLEFT", mainWindow, "TOPLEFT", 40, -7)
	StdUi:AddLabel(mainWindow, searchBox, "", "TOP")

	local raidLeaderName = mainWindow:CreateFontString(nil, "OVERLAY", "GameFontNormal")
	raidLeaderName:SetPoint("TOP", searchBox, "TOP", 125, -7)
	raidLeaderName:SetText("{RLNAME}")

	-- Создаем текст с информацией о рейде
	local raidInfoText = mainWindow:CreateFontString(nil, "OVERLAY", "GameFontNormal")
	raidInfoText:SetPoint("LEFT", raidLeaderName, "RIGHT", 10, 0)
	
	-- Функция обновления информации о рейде
	local function UpdateRaidInfo()
		local lastRaidID = Main.db.global.lastRaidID
		local raidInfo = ""
		if lastRaidID and Main.db.global.RaidDB[lastRaidID] then
			local raidData = Main.db.global.RaidDB[lastRaidID]
			raidInfo = string.format("ID: %s", lastRaidID)
		end
		raidInfoText:SetText(raidInfo)
	end

	-- Инициализация текста
	UpdateRaidInfo()

	-- Создаем фрейм для отслеживания изменений
	local updateFrame = CreateFrame("Frame")
	updateFrame:RegisterEvent("PLAYER_ENTERING_WORLD")
	updateFrame:RegisterEvent("ADDON_LOADED")
	updateFrame:SetScript("OnEvent", function(self, event)
		UpdateRaidInfo()
	end)

	-- Добавляем метод для обновления информации о рейде
	mainWindow.UpdateRaidInfo = UpdateRaidInfo

	-- Сохраняем ссылку на функцию обновления в модуле
	self.UpdateMainWindowRaidInfo = UpdateRaidInfo

	-- Устанавливаем обработчик изменения текста для обновления таблицы
	searchBox:SetScript("OnTextChanged", function()
		AlphaProtocol.ManagmentModule:FilterTable(searchBox:GetText())
	end)
	-----------------------------------------------------------------------------------------------------------------------------------

	-----------------------------------------------------------------------------------------------------------------------------------
	self.mainWindow = mainWindow
	self.mainWindow.playerTable = mainWindow.playerTable
	self.mainWindow.labelText = labelText

	-- Добавляем кнопку для отображения скрытых игроков
	local hiddenPlayersButton = StdUi:Button(mainWindow, 120, 20, "Hidden players")
	StdUi:GlueRight(hiddenPlayersButton, raidInfoText, 10, 0)
	hiddenPlayersButton:SetScript("OnClick", function()
		self:ToggleHiddenPlayersWindow()
	end)
end

-------------------------------------------------------------------------
-------------------- Боковая панель -------------------------------------
-------------------------------------------------------------------------
function InterfaceModule:SidebarWindow()
	local height = self.mainWindow:GetHeight()
	-- Назначаем sidebarWindow как свойство mainWindow
	local sidebarWindow = StdUi:Panel(self.mainWindow, 320, height)
	sidebarWindow:SetPoint("RIGHT", self.mainWindow, "LEFT", 0, 0)
	sidebarWindow:SetFrameLevel(self.mainWindow:GetFrameLevel())
	sidebarWindow:Hide()

	local sidebarMultiLineBox = StdUi:MultiLineBox(sidebarWindow, 280, 300, nil)
	sidebarMultiLineBox.scrollFrame.stdUi = StdUi
	if sidebarMultiLineBox.scrollFrame.ScrollBar then
		sidebarMultiLineBox.scrollFrame.ScrollBar.stdUi = StdUi
	end
	sidebarMultiLineBox:SetAlpha(0.75)
	StdUi:GlueAcross(sidebarMultiLineBox, sidebarWindow, 10, -50, -10, 10)
	sidebarMultiLineBox:SetFocus()

	-- Расстояние между кнопками
	local buttonSpacing = 5

	-- Ширина кнопки
	local buttonWidth = 70

	-- Высота кнопки
	local buttonHigh = 25

	-- Отступ вверх/вниз
	local startPositionY = -15

	-- Общая ширина всех кнопок с учетом пространства между ними
	local totalWidth = (5 * buttonWidth) + (4 * buttonSpacing)

	-- Начальная точка первой кнопки (отрицательная половина общей ширины, чтобы начать с левого края)
	local startPositionX = -(totalWidth / 2) + (buttonWidth / 2)

	-- Создание и размещение кнопок
	local clearInviteButton = StdUi:Button(sidebarWindow, buttonWidth, buttonHigh, "Clear/ADD")
	StdUi:GlueTop(clearInviteButton, sidebarWindow, startPositionX, startPositionY, "CENTER")
	clearInviteButton:SetScript("OnClick", function()
		-- Получаем текст из editBox
		local clearAddText = self.sidebarWindow.editBox:GetText()
		if clearAddText and clearAddText ~= "" then
			-- Очищаем текст от пустых строк и пробелов в начале
			local cleanedText = string.match(clearAddText, "^[\n\r%s]*(.*)")
			
			-- Получаем первую строку очищенного текста
			local firstLine = string.match(cleanedText, "^([^\n\r]*)")
			local isVA = firstLine and string.match(firstLine, "^VA_") ~= nil
			
			if isVA then
				-- Очищаем главное окно и lastRaidID
				AlphaProtocol.ManagmentModule:UpdateMainWindow()
				Main.db.global.lastRaidID = nil
				wipe(AlphaProtocol.ManagmentModule.playersToInvite)
				
				-- Используем очищенный текст для добавления информации о рейде
				AlphaProtocol.ManagmentModule:AddRaidInformation(true, cleanedText)
				AlphaProtocol.InterfaceModule:UpdateMainWindowRaidInfo()
				
				-- Обновляем окно скрытых игроков, если оно открыто
				if self.hiddenPlayersWindow then
					self:UpdateHiddenPlayersTable()
				end
				
				-- Обновляем время последней синхронизации
				self:UpdateLastSyncTime()
				-- Обновляем кэш статусов и проверяем изменения статусов
				if AlphaProtocol.StatusSyncModule then
					AlphaProtocol.StatusSyncModule:InitializeStatusCache()
					AlphaProtocol.StatusSyncModule:ForceSyncAllStatuses()
				end
				
				-- Отправляем очищенный текст Clear/ADD другим участникам если включена автоматическая отправка
				if Main.db.profile.autoSendClearAdd2 then
					AlphaProtocol.OtherModule:SendClearAddText(cleanedText)
				end
			else
				-- Симулируем нажатие кнопки UPDATE
				AlphaProtocol.ManagmentModule:AddRaidInformation(false, cleanedText)
				AlphaProtocol.OtherModule:SendUpdateText(cleanedText)
				
				-- Проверяем изменения статусов после обновления
				if AlphaProtocol.StatusSyncModule then
					AlphaProtocol.StatusSyncModule:CheckStatusChanges()
				end
			end
		end
	end)

	--[[
	local addOneButton = StdUi:Button(sidebarWindow, buttonWidth, buttonHigh, "UPDATE")
	StdUi:GlueTop(addOneButton, sidebarWindow, startPositionX + buttonWidth + buttonSpacing, startPositionY, "CENTER")
	addOneButton:SetScript("OnClick", function()
		-- Получаем текст обновления
		local updateText = self.sidebarWindow.editBox:GetText()
		if updateText and updateText ~= "" then
			-- Применяем обновление локально
			AlphaProtocol.ManagmentModule:AddRaidInformation(false)
			-- Отправляем текст обновления другим участникам
			AlphaProtocol.OtherModule:SendUpdateText(updateText)
		end
	end)
	]]

	local copyClientsButton = StdUi:Button(sidebarWindow, buttonWidth, buttonHigh, "Copy Clients")
	StdUi:GlueTop(
		copyClientsButton,
		sidebarWindow,
		startPositionX + (1 * (buttonWidth + buttonSpacing)),
		startPositionY,
		"CENTER"
	)
	copyClientsButton:SetScript("OnClick", function()
		self.sidebarWindow.editBox:SetText("") -- Очистка текстового поля
		local copyClientsData = AlphaProtocol.ExportModule:CopyClientsExport()
		self.sidebarWindow.editBox:SetText(copyClientsData) -- Заполнение данными о клиентах
	end)

	local problemPlayersButton = StdUi:Button(sidebarWindow, buttonWidth, buttonHigh, "Problems")
	StdUi:GlueTop(
		problemPlayersButton,
		sidebarWindow,
		startPositionX + (2 * (buttonWidth + buttonSpacing)),
		startPositionY,
		"CENTER"
	)
	problemPlayersButton:SetScript("OnClick", function()
		self.sidebarWindow.editBox:SetText("") -- Очистка текстового поля
		local problemPlayersData = AlphaProtocol.ExportModule:ProblemsExport()
		self.sidebarWindow.editBox:SetText(problemPlayersData) -- Заполнение данными о проблемах игроков
	end)

	local raidExportButton = StdUi:Button(sidebarWindow, buttonWidth, buttonHigh, "Final export")
	StdUi:GlueTop(
		raidExportButton,
		sidebarWindow,
		startPositionX + (4 * (buttonWidth + buttonSpacing)),
		startPositionY,
		"CENTER"
	)
	raidExportButton:SetScript("OnClick", function()
		self.sidebarWindow.editBox:SetText("") -- Очистка текстового поля
		local exportData = AlphaProtocol.ExportModule:FinalExport()
		self.sidebarWindow.editBox:SetText(exportData) -- Заполнение всей информацией о рейде
		self.sidebarWindow.editBox.scrollFrame.scrollChild:HighlightText() -- Выделение всего текста для удобства копирования
		self.sidebarWindow.editBox.scrollFrame.scrollChild:SetFocus()
	end)

	self.sidebarWindow = sidebarWindow
	self.sidebarWindow.editBox = sidebarMultiLineBox
end
-------------------------------------------------------------------------
-------------------- Верхняя панель -------------------------------------
-------------------------------------------------------------------------
function InterfaceModule:OptionalWindow()
	if not self.mainWindow then
		return
	end

	local width = self.mainWindow:GetWidth()
	-- Назначаем optionalWindow как свойство mainWindow
	local optionalWindow = StdUi:Panel(self.mainWindow, width, -40)
	optionalWindow:SetPoint("TOP", self.mainWindow, "TOP", 0, 0)
	optionalWindow:SetFrameLevel(self.mainWindow:GetFrameLevel())
	optionalWindow:Hide()



	local sortingButton = StdUi:Button(optionalWindow, 60, 20, "Sorting")
	sortingButton:SetPoint("LEFT", optionalWindow, "LEFT", 10, 0)
	sortingButton:SetScript("OnClick", function()
		AlphaProtocol.ManagmentModule:DistributePlayers()
	end)

	local synchroButton = StdUi:Button(optionalWindow, 100, 20, "Synchronization")
	synchroButton:SetPoint("LEFT", sortingButton, "RIGHT", 10, 0)
	synchroButton:SetScript("OnClick", function()
		InterfaceModule:createConfirmationDialog(function(yes)
			if yes then
				AlphaProtocol.OtherModule:SendCommMessage()
			end
		end)
	end)

	-- Получаем тексты из настроек
	local items = {
		{ text = Main.db.profile.customText1, value = 1 },
		{ text = Main.db.profile.customText2, value = 2 },
		{ text = Main.db.profile.customText3, value = 3 },
	}

	local dropDownText = StdUi:Dropdown(optionalWindow, 200, 20, items)
	dropDownText:SetPoint("LEFT", synchroButton, "RIGHT", 120, 0)
	-- Фиксируем размер дропдауна
	dropDownText:SetWidth(200)
	if dropDownText.optsFrame then
		dropDownText.optsFrame:SetWidth(200)
		dropDownText.optsFrame:ClearAllPoints()
		dropDownText.optsFrame:SetPoint("TOPLEFT", dropDownText, "BOTTOMLEFT", 0, -2)
	end
	-- Предотвращаем изменение размера при обновлении опций
	local originalSetOptions = dropDownText.SetOptions
	dropDownText.SetOptions = function(self, newOptions)
		originalSetOptions(self, newOptions)
		self:SetWidth(200)
		if self.optsFrame then
			self.optsFrame:SetWidth(200)
			self.optsFrame:ClearAllPoints()
			self.optsFrame:SetPoint("TOPLEFT", self, "BOTTOMLEFT", 0, -2)
		end
	end
	function dropDownText:OnValueChanged(value, text)
		--print("Selected text: " .. text)
		AlphaProtocol.ManagmentModule.predefinedMessageText = text
	end

	-- Версия
	local versionFrame = optionalWindow:CreateFontString(nil, "OVERLAY", "GameFontNormal")
	versionFrame:SetPoint("RIGHT", optionalWindow, "RIGHT", -10, 0)
	versionFrame:SetText("ver. 0.23mo")

	self.dropDownText = dropDownText
	self.synchroButton = synchroButton
	self.sortingButton = sortingButton
	self.optionalWindow = optionalWindow
end



-- Функция для обновления текстов в Dropdown
function InterfaceModule:updateDropdownItems()
	-- Удаляем старый Dropdown, если он существует
	if self.dropDownText then
		self.dropDownText:Hide()
		self.dropDownText = nil
	end

	-- Создаем новый Dropdown с обновленными данными
	local items = {
		{ text = Main.db.profile.customText1, value = 1 },
		{ text = Main.db.profile.customText2, value = 2 },
		{ text = Main.db.profile.customText3, value = 3 },
	}

	self.dropDownText = StdUi:Dropdown(self.optionalWindow, 200, 20, items, nil, nil)
	self.dropDownText:SetPoint("LEFT", self.synchroButton, "RIGHT", 120, 0)
	-- Фиксируем размер дропдауна
	self.dropDownText:SetWidth(200)
	if self.dropDownText.optsFrame then
		self.dropDownText.optsFrame:SetWidth(200)
		self.dropDownText.optsFrame:ClearAllPoints()
		self.dropDownText.optsFrame:SetPoint("TOPLEFT", self.dropDownText, "BOTTOMLEFT", 0, -2)
	end
	-- Предотвращаем изменение размера при обновлении опций
	local originalSetOptions = self.dropDownText.SetOptions
	self.dropDownText.SetOptions = function(self, newOptions)
		originalSetOptions(self, newOptions)
		self:SetWidth(200)
		if self.optsFrame then
			self.optsFrame:SetWidth(200)
			self.optsFrame:ClearAllPoints()
			self.optsFrame:SetPoint("TOPLEFT", self, "BOTTOMLEFT", 0, -2)
		end
	end
	self.dropDownText.OnValueChanged = function(_, value, text)
		--print("Selected text: " .. text)
		AlphaProtocol.ManagmentModule.predefinedMessageText = text
	end
end

-- Окно подтверждения синхронизации
function InterfaceModule:createConfirmationDialog(onConfirm)
	if not self.dialogConfirmWindow then
		self.dialogConfirmWindow = StdUi:Window(UIParent, 300, 100, "Are you sure?")
		self.dialogConfirmWindow:SetPoint("CENTER")
		self.dialogConfirmWindow:SetFrameStrata("DIALOG")
		self.dialogConfirmWindow:SetFrameLevel(self:GetNextFrameLevel() + 100)
		
		-- Добавляем возможность закрытия окна по ESC
		self:EnableEscapeKey(self.dialogConfirmWindow)

		-- Кнопка "Да"
		local yesButton = StdUi:Button(self.dialogConfirmWindow, 100, 24, "YES")
		yesButton:SetPoint("BOTTOMLEFT", self.dialogConfirmWindow, "BOTTOMLEFT", 10, 10)
		yesButton:SetScript("OnClick", function()
			self.dialogConfirmWindow:Hide()
			onConfirm(true)
		end)

		-- Кнопка "Нет"
		local noButton = StdUi:Button(self.dialogConfirmWindow, 100, 24, "NO")
		noButton:SetPoint("BOTTOMRIGHT", self.dialogConfirmWindow, "BOTTOMRIGHT", -10, 10)
		noButton:SetScript("OnClick", function()
			self.dialogConfirmWindow:Hide()
			onConfirm(false)
		end)

		self.dialogConfirmWindow:Show()
	else
		self.dialogConfirmWindow:SetFrameStrata("DIALOG")
		self.dialogConfirmWindow:SetFrameLevel(self:GetNextFrameLevel() + 100)
		self:ToggleFrameVisibility(self.dialogConfirmWindow)
	end
end
-------------------------------------------------------------------------
-------------------- История рейдов -------------------------------------
-------------------------------------------------------------------------
function InterfaceModule:CreateRaidHistory()
	local raidHistory = StdUi:Window(UIParent, 1450, 600, "Raid History")
	raidHistory:SetPoint("CENTER")
	raidHistory:SetFrameLevel(self:GetNextFrameLevel())
	raidHistory:Hide()
	
	-- Добавляем возможность закрытия окна по ESC
	self:EnableEscapeKey(raidHistory)

	-- Добавляем обработчик клика для активации окна
	raidHistory:SetScript("OnMouseDown", function(self)
		self:SetFrameLevel(InterfaceModule:GetNextFrameLevel())
	end)

	-- Создаем панель выбора рейда (левая)
	local selectionPanel = StdUi:Panel(raidHistory, 260, raidHistory:GetHeight() - 60) -- Увеличили с 200 до 260
	selectionPanel:SetPoint("TOPLEFT", raidHistory, "TOPLEFT", 10, -50)

	-- Поле поиска
	local searchBox = StdUi:EditBox(selectionPanel, 240, 24, "") -- Увеличили с 180 до 240
	searchBox:SetPoint("TOP", selectionPanel, "TOP", 0, -10)
	
	-- Добавляем placeholder текст
	searchBox.placeholder = searchBox:CreateFontString(nil, "OVERLAY", "GameFontNormal")
	searchBox.placeholder:SetPoint("LEFT", searchBox, "LEFT", 5, 0)
	searchBox.placeholder:SetText("Search Raid...")
	searchBox.placeholder:SetTextColor(0.5, 0.5, 0.5, 1)
	
	-- Обработчики для placeholder
	searchBox:SetScript("OnEditFocusGained", function(self)
		self.placeholder:Hide()
	end)
	
	searchBox:SetScript("OnEditFocusLost", function(self)
		if self:GetText() == "" then
			self.placeholder:Show()
		end
	end)
	
	searchBox:SetScript("OnTextChanged", function(self)
		if self:GetText() ~= "" then
			self.placeholder:Hide()
		else
			self.placeholder:Show()
		end
	end)

	-- Создаем скроллирующий список для рейдов
	local raidList = StdUi:ScrollFrame(selectionPanel, 240, selectionPanel:GetHeight() - 44) -- Увеличили с 180 до 240
	raidList:SetPoint("TOP", searchBox, "BOTTOM", 0, -10)

	-- Создаем центральную панель для текстовой информации
	local centerPanel = StdUi:Panel(raidHistory, 420, raidHistory:GetHeight() - 60) -- Уменьшили с 450 до 420
	centerPanel:SetPoint("LEFT", selectionPanel, "RIGHT", 10, 0)

	-- Создаем правую панель для визуализации
	local rightPanel = StdUi:Panel(raidHistory, 720, raidHistory:GetHeight() - 60) -- Уменьшили с 750 до 720
	rightPanel:SetPoint("LEFT", centerPanel, "RIGHT", 10, 0)

	-- Заголовок для правой панели
	local rightPanelTitle = rightPanel:CreateFontString(nil, "OVERLAY", "GameFontNormalLarge")
	rightPanelTitle:SetPoint("TOP", rightPanel, "TOP", 0, -10)
	rightPanelTitle:SetText("Loot History")

	-- Добавляем кнопку черного списка
	local blacklistButton = StdUi:Button(rightPanel, 100, 20, "Черный список")
	blacklistButton:SetPoint("TOPRIGHT", rightPanel, "TOPRIGHT", -10, -10)
	blacklistButton:SetScript("OnClick", function()
		AlphaProtocol.LootModule:CreateBlacklistWindow()
	end)
	rightPanel.blacklistButton = blacklistButton -- Сохраняем ссылку на кнопку в панели

	-- Создаем MultiLineBox в центральной панели
	local multiLineNewBox = StdUi:MultiLineBox(centerPanel, centerPanel:GetWidth() - 20, centerPanel:GetHeight() - 20, nil)
	multiLineNewBox.scrollFrame.stdUi = StdUi
	if multiLineNewBox.scrollFrame.ScrollBar then
		multiLineNewBox.scrollFrame.ScrollBar.stdUi = StdUi
	end
	multiLineNewBox:SetAlpha(0.75)
	StdUi:GlueAcross(multiLineNewBox, centerPanel, 10, -10, -10, 10)

	-- Функция для создания кнопки рейда
	local function CreateRaidButton(raidInfo, index)
		local button = StdUi:Button(raidList.scrollChild, 230, 30, raidInfo.text) -- Увеличили с 170 до 230
		button:SetPoint("TOPLEFT", raidList.scrollChild, "TOPLEFT", 5, -((index - 1) * 35))
		
		-- Стилизация кнопки
		button.text:SetJustifyH("LEFT")
		button:SetScript("OnClick", function()
			AlphaProtocol.ExportModule:HistoryExport(raidInfo.value, multiLineNewBox)
			AlphaProtocol.LootModule:RefreshLootData() -- Обновляем общие данные лута
			InterfaceModule:UpdateRaidVisualization(raidInfo.value, rightPanel)
			
			-- Подсветка выбранной кнопки
			for _, child in pairs({raidList.scrollChild:GetChildren()}) do
				if child ~= button then
					child:SetBackdropColor(0.1, 0.1, 0.1, 0.9)
				end
			end
			button:SetBackdropColor(0.2, 0.4, 0.8, 0.9)
		end)
		return button
	end

	-- Функция обновления списка рейдов
	local function UpdateRaidList(searchText)
		-- Очищаем текущий список
		for _, child in pairs({raidList.scrollChild:GetChildren()}) do
			child:Hide()
			child:SetParent(nil)
		end

		-- Получаем список рейдов
		local items = AlphaProtocol.ExportModule:GetRaidListForDropdown()
		local visibleButtons = 0

		-- Фильтруем и создаем кнопки
		for i, raidInfo in ipairs(items) do
			if not searchText or searchText == "" or string.find(string.lower(raidInfo.text), string.lower(searchText)) then
				visibleButtons = visibleButtons + 1
				local button = CreateRaidButton(raidInfo, visibleButtons)
				button:Show()
			end
		end

		-- Обновляем размер скролл фрейма
		raidList.scrollChild:SetHeight(math.max(visibleButtons * 35, raidList:GetHeight()))
	end

	-- Обработчик поиска
	searchBox:SetScript("OnTextChanged", function(self)
		UpdateRaidList(self:GetText())
	end)

	-- Инициализация списка рейдов
	UpdateRaidList("")

	-- Добавляем обработчик показа окна для обновления данных
	raidHistory:SetScript("OnShow", function()
		AlphaProtocol.LootModule:RefreshLootData() -- Обновляем общие данные лута
		-- Если есть выбранный рейд, обновляем его визуализацию
		if Main.db.global.lastRaidID then
			AlphaProtocol.ExportModule:HistoryExport(Main.db.global.lastRaidID, multiLineNewBox)
			InterfaceModule:UpdateRaidVisualization(Main.db.global.lastRaidID, rightPanel)
		end
	end)

	self.raidHistory = raidHistory
end

-- Функция для обновления визуализации данных рейда
function InterfaceModule:UpdateRaidVisualization(raidID, panel)
	-- Очищаем предыдущую визуализацию
	if panel.lootTable then
		panel.lootTable:Hide()
		panel.lootTable:SetParent(nil)
		panel.lootTable = nil
	end
	
	for _, child in pairs({panel:GetChildren()}) do
		if child ~= panel.titleText and child ~= panel.blacklistButton then -- Сохраняем кнопку черного списка
			child:Hide()
			child:SetParent(nil)
		end
	end

	local raidData = Main.db.global.RaidDB[raidID]
	if not raidData then 
		return 
	end

	-- Создаем панель фильтров
	local filterPanel = StdUi:Panel(panel, panel:GetWidth() - 20, 30)
	filterPanel:SetPoint("TOPLEFT", panel, "TOPLEFT", 10, -30)
	filterPanel:SetPoint("TOPRIGHT", panel, "TOPRIGHT", -10, -30)

	-- Добавляем метку для выпадающего списка
	local bossLabel = StdUi:Label(filterPanel, "Босс:", 12)
	bossLabel:SetPoint("LEFT", filterPanel, "LEFT", 5, 0)

	-- Функция для получения списка боссов
	local function GetBossList()
		local bosses = {}
		local bossesSet = {}

		-- Добавляем опцию "Весь лут"
		table.insert(bosses, {text = "Весь лут", value = "all"})

		if raidData.players then
			for _, playerData in pairs(raidData.players) do
				if playerData.playerLoot then
					for _, lootData in ipairs(playerData.playerLoot) do
						local bossName = lootData.bossName or "Unknown"
						if not bossesSet[bossName] then
							bossesSet[bossName] = true
							table.insert(bosses, {text = bossName, value = bossName})
						end
					end
				end
			end
		end

		-- Сортируем боссов по имени, но Unknown всегда в конце
		table.sort(bosses, function(a, b)
			if a.value == "all" then return true end
			if b.value == "all" then return false end
			if a.value == "Unknown" then return false end
			if b.value == "Unknown" then return true end
			return a.text < b.text
		end)

		return bosses
	end

	-- Создаем поле поиска
	local searchBox = StdUi:EditBox(filterPanel, 180, 20, "")
	searchBox:SetPoint("LEFT", filterPanel, "CENTER", 10, 0)

	-- Добавляем иконку поиска
	local searchIcon = filterPanel:CreateTexture(nil, "ARTWORK")
	searchIcon:SetTexture("Interface\\Common\\UI-Searchbox-Icon")
	searchIcon:SetSize(14, 14)
	searchIcon:SetPoint("RIGHT", searchBox, "RIGHT", -5, 0)
	searchIcon:SetVertexColor(0.6, 0.6, 0.6, 0.8)

	-- Добавляем placeholder текст
	searchBox.placeholder = searchBox:CreateFontString(nil, "OVERLAY", "GameFontNormal")
	searchBox.placeholder:SetPoint("LEFT", searchBox, "LEFT", 5, 0)
	searchBox.placeholder:SetText("Поиск...")
	searchBox.placeholder:SetTextColor(0.5, 0.5, 0.5, 1)

	-- Настраиваем внешний вид поля поиска
	searchBox:SetTextInsets(5, 20, 0, 0)

	-- Создаем выпадающий список
	local bossDropdown = StdUi:Dropdown(filterPanel, 180, 20, GetBossList())
	bossDropdown:SetPoint("LEFT", bossLabel, "RIGHT", 5, 0)
	bossDropdown.optsFrame.itemListHeight = 400 -- Устанавливаем максимальную высоту списка
	bossDropdown:SetValue("all")

	-- Вычисляем доступную ширину для таблицы
	local availableWidth = panel:GetWidth() - 20 -- отступы по 10 пикселей с каждой стороны
	local bossWidth = math.floor(availableWidth * 0.2) -- 20% для босса
	local playerWidth = math.floor(availableWidth * 0.2) -- 20% для игрока
	local timeWidth = math.floor(availableWidth * 0.3) -- 20% для времени
	local itemWidth = availableWidth - bossWidth - playerWidth - timeWidth -- оставшееся для предмета

	-- Создаем колонки для таблицы
	local tableColumns = {
		{
			name = "Boss",
			width = bossWidth,
			align = "LEFT",
			index = "bossName",
			defaultSort = "asc",
		},
		{
			name = "Player",
			width = playerWidth,
			align = "LEFT",
			index = "playerName",
		},
		{
			name = "Item",
			width = itemWidth,
			align = "LEFT",
			index = "itemLink",
			events = {
				OnEnter = function(table, cellFrame, rowFrame, rowData, columnData, rowIndex)
					GameTooltip:SetOwner(cellFrame, "ANCHOR_RIGHT")
					GameTooltip:SetHyperlink(rowData.itemLink)
					GameTooltip:Show()
				end,
				OnLeave = function(table, cellFrame)
					GameTooltip:Hide()
				end,
			},
		},
		{
			name = "Time",
			width = timeWidth,
			align = "LEFT",
			index = "timestamp",
			format = function(value)
				if not value then return "" end
				-- Получаем текущее время сервера через игровое API
				local hours, minutes = GetGameTime()
				local serverTime = hours * 3600 + minutes * 60
				
				-- Получаем UTC время
				local utcTime = GetServerTime()
				local utcHours = tonumber(date("!%H", utcTime))
				local utcMinutes = tonumber(date("!%M", utcTime))
				local utcSeconds = utcHours * 3600 + utcMinutes * 60
				
				-- Вычисляем смещение для отображения
				local offset = serverTime - utcSeconds
				if offset > 43200 then -- более 12 часов
					offset = offset - 86400
				elseif offset < -43200 then
					offset = offset + 86400
				end
				
				return date("!%d.%m.%Y %H:%M:%S", value + offset)
			end,
		},
	}

	-- Создаем таблицу
	panel.lootTable = StdUi:ScrollTable(panel, tableColumns, 22, 20)
	panel.lootTable:SetPoint("TOPLEFT", filterPanel, "BOTTOMLEFT", 0, -20) -- Увеличиваем отступ с -10 до -20
	panel.lootTable:SetPoint("BOTTOMRIGHT", panel, "BOTTOMRIGHT", -10, 10)

	-- Функция обновления данных таблицы
	local function UpdateLootList(selectedBoss, searchText)
		local tableData = {}
		
		if raidData.players then
			for _, playerData in pairs(raidData.players) do
				if playerData.playerLoot then
					for _, lootData in ipairs(playerData.playerLoot) do
						if type(lootData) == "table" and lootData.timestamp and lootData.itemLink then
							-- Проверяем, не находится ли предмет в черном списке (НОВАЯ ЛОГИКА)
							local isBlacklisted = false
							local itemLink = lootData.itemLink
							local _, itemName = GetItemInfo(itemLink)
							itemName = itemName and itemName:lower() or ""
							local tooltipText = AlphaProtocol.LootModule:GetFullTooltipTextForItem(itemLink) -- Используем функцию из LootModule

							if Main.db.global.LootBlacklist then
								for _, blacklistedString in ipairs(Main.db.global.LootBlacklist) do
									local searchString = blacklistedString:lower()
									-- Проверяем наличие строки из черного списка В ИМЕНИ или В ТЕКСТЕ ПОДСКАЗКИ
									if searchString ~= "" and (itemName:find(searchString, 1, true) or tooltipText:find(searchString, 1, true)) then
										isBlacklisted = true
										break
									end
								end
							end

							local currentBossName = lootData.bossName or "Unknown"
							
							if not isBlacklisted and
							   (selectedBoss == "all" or selectedBoss == currentBossName) and
							   (not searchText or searchText == "" or 
								currentBossName:lower():find(searchText:lower()) or
								(lootData.playerName or playerData.playerName):lower():find(searchText:lower()) or
								lootData.itemLink:lower():find(searchText:lower())) then
								table.insert(tableData, {
									bossName = currentBossName,
									playerName = lootData.playerName or playerData.playerName or "",
									itemLink = lootData.itemLink,
									timestamp = lootData.timestamp
								})
							end
						end
					end
				end
			end
		end

		-- Сортируем по времени (от новых к старым) и по боссам
		if #tableData > 0 then
			table.sort(tableData, function(a, b)
				if a.bossName == b.bossName then
					if a.timestamp == b.timestamp then
						return a.playerName < b.playerName
					end
					return (a.timestamp or 0) > (b.timestamp or 0)
				end
				return a.bossName < b.bossName
			end)
		end

		panel.lootTable:SetData(tableData)
	end

	-- Обработчики событий
	searchBox:SetScript("OnTextChanged", function(self)
		local text = self:GetText()
		if text == "" then
			self.placeholder:Show()
		else
			self.placeholder:Hide()
		end
		UpdateLootList(bossDropdown:GetValue(), text)
	end)

	searchBox:SetScript("OnEditFocusGained", function(self)
		self.placeholder:Hide()
	end)

	searchBox:SetScript("OnEditFocusLost", function(self)
		if self:GetText() == "" then
			self.placeholder:Show()
		end
	end)

	bossDropdown.OnValueChanged = function(self, value)
		UpdateLootList(value, searchBox:GetText())
	end

	-- Инициализация данных
	UpdateLootList("all", "")
end

-------------------------------------------------------------------------
-------------------- Напоминание об одиночных боссах --------------------
-------------------------------------------------------------------------
-- Вспомогательная функция для инициализации окна уведомлений
function InterfaceModule:InitNotificationWindow()
	if not self.notificationWindow then
		self.notificationWindow = StdUi:Window(UIParent, 500, 400, "Attention")
		self.notificationWindow:SetPoint("CENTER")
		self.notificationWindow:SetFrameStrata("DIALOG")
		self.notificationWindow.closeBtn:Hide()

		-- Создаем MultiLineBox для накопления уведомлений
		self.notificationMultiLineBox = StdUi:MultiLineBox(self.notificationWindow, 460, 270)
		self.notificationMultiLineBox.scrollFrame.stdUi = StdUi
		if self.notificationMultiLineBox.scrollFrame.ScrollBar then
			self.notificationMultiLineBox.scrollFrame.ScrollBar.stdUi = StdUi
		end
		self.notificationMultiLineBox:SetAlpha(0.75)
		StdUi:GlueAcross(self.notificationMultiLineBox, self.notificationWindow, 10, -50, -10, 40)

		-- Устанавливаем обработчик, который не позволит изменять текст
		self.notificationMultiLineBox.editBox:SetScript("OnTextChanged", function(self)
			self:SetText(self:GetParent():GetParent().previousText or "")
		end)

		-- Кнопка закрытия
		self.closeButton = StdUi:Button(self.notificationWindow, 100, 24, "Close")
		self.closeButton:SetPoint("BOTTOM", self.notificationWindow, "BOTTOM", 0, 10)
		self.closeButton:SetScript("OnClick", function()
			self.notificationWindow:Hide()
			self.notificationMultiLineBox:SetText("") -- Очищаем текст при закрытии
			if self.mainWindow and not self.mainWindow:IsShown() then
				self:ToggleFrameVisibility(self.mainWindow)
			end
		end)

		-- Закрытие по Escape
		self:EnableEscapeKey(self.notificationWindow)
		self.notificationWindow:HookScript("OnHide", function()
			self.notificationMultiLineBox:SetText("") -- Очищаем текст при закрытии по ESC
		end)
	end
	
	return self.notificationWindow
end

function InterfaceModule:ShowNotification(playerName, bossName)
	-- Инициализация окна уведомлений
	self:InitNotificationWindow()

	-- Добавляем новую строку в MultiLineBox и сохраняем текущий текст для восстановления
	local newLine = "Don't forget to invite " .. playerName .. " to boss " .. bossName
	local currentText = self.notificationMultiLineBox:GetText()
	if currentText ~= "" then
		newLine = currentText .. "\n" .. newLine
	end
	self.notificationMultiLineBox:SetText(newLine)
	self.notificationMultiLineBox.previousText = newLine -- Сохраняем текущий текст

	-- Показываем фрейм
	self:SetTopFrameLevel(self.notificationWindow)
	self.notificationWindow:Show()
end

-- Новая функция для обработки нескольких уведомлений сразу
function InterfaceModule:ShowBatchNotifications(notificationsList)
	-- Проверка на пустой список
	if not notificationsList or #notificationsList == 0 then
		return
	end
	
	-- Инициализация окна уведомлений
	self:InitNotificationWindow()

	-- Формируем текст уведомления из всех элементов списка
	local notificationText = ""
	for i, notification in ipairs(notificationsList) do
		if i > 1 then
			notificationText = notificationText .. "\n"
		end
		notificationText = notificationText .. "Don't forget to invite " .. notification.name .. " to boss " .. notification.boss
	end
	
	-- Устанавливаем текст и сохраняем его
	self.notificationMultiLineBox:SetText(notificationText)
	self.notificationMultiLineBox.previousText = notificationText
	
	-- Показываем фрейм
	self:SetTopFrameLevel(self.notificationWindow)
	self.notificationWindow:Show()
end

-------------------------------------------------------------------------
-------------------- Чтение и копирование информации оператора ----------
-------------------------------------------------------------------------
function InterfaceModule:SetupEditBoxForOperatorInfo(playerID, playerName, raidID, cellFrame, rowData)
	-- Проверяем, существует ли уже созданный Frame, если нет, создаем новый с использованием StdUi
	if not self.infoFrame then
		-- Создание нового Frame для информации
		self.infoFrame = StdUi:Window(UIParent, 300, 200, "Operator Info")
		self.infoFrame:SetPoint("CENTER")

		-- Создание EditBox внутри Frame для отображения информации
		self.infoEditBox = StdUi:MultiLineBox(self.infoFrame, 280, 150, "")
		self.infoEditBox:SetPoint("TOP", 0, -30)
		self.infoEditBox:SetFocus()

		-- Эта настройка позволяет окну не скрываться автоматически при открытии других элементов интерфейса
		self.infoFrame:SetFrameStrata("HIGH")
		
		-- Добавляем возможность закрытия окна по ESC
		self:EnableEscapeKey(self.infoFrame)
	end

	-- Устанавливаем текст из данных игрока и показываем окно с максимальным уровнем фрейма
	self.infoEditBox:SetText(rowData.info or "")
	self:SetTopFrameLevel(self.infoFrame)
	self.infoFrame:Show()
end
-------------------------------------------------------------------------
-------------------- Чтение и копирование информации игрока -------------
-------------------------------------------------------------------------
function InterfaceModule:SetupEditBoxForPlayerInfo(playerID, playerName, raidID, cellFrame, rowData)
	-- Используем существующий EditBox или создаем новый, если его еще нет
	local playerNameEditBox = self.playerNameEditBox or CreateFrame("EditBox", nil, UIParent, "InputBoxTemplate")
	self.playerNameEditBox = playerNameEditBox

	-- Настройка EditBox
	playerNameEditBox:SetAutoFocus(true)
	playerNameEditBox:SetFrameStrata("TOOLTIP")
	playerNameEditBox:SetFrameLevel(100)
	playerNameEditBox:SetSize(cellFrame:GetWidth(), cellFrame:GetHeight())
	playerNameEditBox:SetPoint("TOPLEFT", cellFrame, "TOPLEFT")
	playerNameEditBox:SetPoint("BOTTOMRIGHT", cellFrame, "BOTTOMRIGHT")
	local playerInfoText = (rowData.playerID or "") .. "  " .. (rowData.name or "")
	playerNameEditBox:SetText(playerInfoText)
	playerNameEditBox:Show()
	playerNameEditBox:SetFocus()

	-- Обработчики событий для закрытия и сохранения данных
	playerNameEditBox:SetScript("OnEnterPressed", function(self)
		self:ClearFocus()
		self:Hide()
	end)

	playerNameEditBox:SetScript("OnEscapePressed", function(self)
		self:ClearFocus()
		self:Hide()
	end)
end
-------------------------------------------------------------------------
-------------------- Иконка у миникарты ---------------------------------
-------------------------------------------------------------------------
function InterfaceModule:DrawMinimapButton()
	local minimapButton = LDB:NewDataObject("MinimapButton", {
		type = "launcher",
		text = "FriendshipIsMagic",
		icon = "Interface\\AddOns\\FriendshipIsMagic\\Assets\\TwS.tga",
		OnClick = function(_, _, button)
			if not self.menuMiniFrame then
				local menuFrame = CreateFrame("Frame", "ExampleMenuFrame", UIParent, "UIDropDownMenuTemplate")
				self.menuMiniFrame = menuFrame
			end
			-- Определение элементов меню
			local menuItems = {
				{
					text = "FriendshipIsMagic",
					isTitle = true,
					notCheckable = true,
				},
				{
					text = "Main Window",
					notCheckable = true,
					func = function()
						self:EnsureFrameCreated("mainWindow", "CreateMainWindow")
						self:ToggleFrameVisibility(self.mainWindow)
					end,
				},
				{
					text = "Raid History",
					notCheckable = true,
					func = function()
						self:EnsureFrameCreated("raidHistory", "CreateRaidHistory")
						self:ToggleFrameVisibility(self.raidHistory)
					end,
				},
				{
					text = "Hide Minimap Icon",
					notCheckable = true,
					func = function()
						Main.db.profile.minimap.hide = not Main.db.profile.minimap.hide
						LDBIcon:Refresh("MinimapButton", Main.db.profile.minimap)
					end,
				}, -- Добавьте дополнительные пункты меню здесь
			}
			-- Отображаем меню, используя EasyMenu
			LibEasyMenu:EasyMenu(menuItems, self.menuMiniFrame, "cursor", 0, 0, "MENU")
		end,
		OnEnter = function(anchor)
			-- Создание всплывающего описания
			GameTooltip:SetOwner(anchor, "ANCHOR_LEFT")
			GameTooltip:AddLine("/fim - команды в чате", 0.8, 0.8, 0.8, true)
			GameTooltip:Show()
		end,
		OnLeave = function()
			-- Скрытие всплывающего описания
			GameTooltip:Hide()
		end,
	})

	-- Инициализация иконки на миникарте
	LDBIcon:Register("MinimapButton", minimapButton, Main.db.profile.minimap)
	LDBIcon:Refresh("MinimapButton", Main.db.profile.minimap)
end
-------------------------------------------------------------------------
-------------------- Консольные команды ---------------------------------
-------------------------------------------------------------------------
InterfaceModule.colorString = "|cffd4af37FIM|r"
SLASH_FIM1 = "/FIM"
function SlashCmdList.FIM(msg)
	local command, password = strsplit(" ", msg)

	if command == "password" and password then
		InterfaceModule:CheckPassword(password)
	elseif command == "main" then
		InterfaceModule:CreateMainWindow()
	elseif command == "history" then
		InterfaceModule:EnsureFrameCreated("raidHistory", "CreateRaidHistory")
		InterfaceModule:ToggleFrameVisibility(InterfaceModule.raidHistory)
	elseif command == "minimap" then
		Main.db.profile.minimap.hide = not Main.db.profile.minimap.hide
		LDBIcon:Refresh("MinimapButton", Main.db.profile.minimap)
	elseif command == "cleardb" then
		wipe(Main.db.global)
		print(InterfaceModule.colorString .. "Database cleared.")
	elseif command == "debugbosses" then
		local currentRaid = AlphaProtocol:GetModule("EventsModule"):GetCurrentRaidName()
		if not currentRaid then
			print(InterfaceModule.colorString .. "Вы не находитесь в рейде")
			return
		end
		
		print(InterfaceModule.colorString .. "Текущий рейд: " .. currentRaid)
		print(InterfaceModule.colorString .. "Боссы для задания LoU Single Boss HC:")
		
		local bossList = AlphaProtocol:GetModule("EventsModule"):GetCurrentRaidBosses()
		if not bossList then
			print(InterfaceModule.colorString .. "Список боссов не найден")
			return
		end
		
		for bossName, _ in pairs(bossList) do
			print("- " .. bossName)
		end
	else
		print(InterfaceModule.colorString .. " Options:")
		print("   /" .. InterfaceModule.colorString .. " password <password>")
		print("   /" .. InterfaceModule.colorString .. " main - Open Main Window")
		print("   /" .. InterfaceModule.colorString .. " history - Open Raid History")
		print("   /" .. InterfaceModule.colorString .. " minimap - Show/Hide Minimap")
		print("   /" .. InterfaceModule.colorString .. " cleardb - Clear database")
	end
end

-- Создание окна со списком скрытых игроков
function InterfaceModule:CreateHiddenPlayersWindow()
	if self.hiddenPlayersWindow then
		return
	end

	local height = self.mainWindow:GetHeight()
	-- Создаем панель, прикрепленную к правой стороне основного окна
	local window = StdUi:Panel(self.mainWindow, 320, height)
	window:SetPoint("LEFT", self.mainWindow, "RIGHT", 0, 0)
	window:SetFrameLevel(self.mainWindow:GetFrameLevel())
	
	-- Добавляем заголовок окна
	local titleText = StdUi:FontString(window, "Hidden players")
	StdUi:GlueTop(titleText, window, 0, -10, "CENTER")
	StdUi:SetTextColor(titleText, "header")

	-- Добавляем кнопку закрытия
	local closeButton = StdUi:Button(window, 20, 20, "X")
	closeButton:SetPoint("TOPRIGHT", window, "TOPRIGHT", -5, -5)
	closeButton:SetScript("OnClick", function()
		window:Hide()
	end)

	-- Функция создания контекстного меню
	local function createContextMenu(playerName)
		local menu = {
			{
				text = "Reveal player",
				func = function()
					for _, player in ipairs(AlphaProtocol.ManagmentModule.playersToInvite) do
						if player.name == playerName then
							player.isHide = false
							break
						end
					end
					AlphaProtocol.ManagmentModule:UpdateMainWindow()
					AlphaProtocol.InterfaceModule:UpdateHiddenPlayersTable()
					Main:SavePlayersToRaidDB()
				end,
			},
		}
		local menuFrame = CreateFrame("Frame", "ExampleMenuFrame", UIParent, "UIDropDownMenuTemplate")
		LibEasyMenu:EasyMenu(menu, menuFrame, "cursor", 0, 0, "MENU")
	end

	-- Определяем колонки для таблицы
	local columns = {
		{
			name = "№",
			width = 30,
			align = "CENTER",
			index = "countNumber",
			format = "number",
			events = {
				OnClick = function(_, cellFrame, _, rowData, _, _, _, _, _, ...)
					local buttonClicked = GetMouseButtonClicked()
					if buttonClicked == "RightButton" then
						createContextMenu(rowData.name)
					end
				end,
			},
		},
		{
			name = "Имя",
			width = 150,
			align = "LEFT",
			index = "name",
			format = "string",
			events = {
				OnClick = function(_, cellFrame, _, rowData, _, _, _, _, _, ...)
					local buttonClicked = GetMouseButtonClicked()
					if buttonClicked == "RightButton" then
						createContextMenu(rowData.name)
					end
				end,
			},
		},
		{
			name = "Задача",
			width = 100,
			align = "LEFT",
			index = "Task",
			format = "string",
			events = {
				OnClick = function(_, cellFrame, _, rowData, _, _, _, _, _, ...)
					local buttonClicked = GetMouseButtonClicked()
					if buttonClicked == "RightButton" then
						createContextMenu(rowData.name)
					end
				end,
				OnEnter = function(_, _, cell, data, _, _, _, _, _, ...)
					AlphaProtocol.ManagmentModule:CreateTaskTooltip(cell, data)
				end,
				OnLeave = function(_, _, _, _, _, _, _, _, _, ...)
					AlphaProtocol.ManagmentModule:HideInfoTooltip()
				end,
			},
		},
	}

	-- Создаем таблицу
	local hiddenPlayersTable = StdUi:ScrollTable(window, columns, 12, 20)
	-- Увеличиваем отступ сверху для избежания наезда на заголовок
	StdUi:GlueAcross(hiddenPlayersTable, window, 10, -40, -10, 40)
	window.scrollTable = hiddenPlayersTable

	-- Добавляем кнопку для восстановления игроков
	local revealButton = StdUi:Button(window, 150, 20, "Reveal all")
	StdUi:GlueBottom(revealButton, window, 0, 10, "CENTER")
	revealButton:SetScript("OnClick", function()
		AlphaProtocol.ManagmentModule:RevealAllPlayers()
		self:UpdateHiddenPlayersTable()
	end)

	window:Hide()
	self.hiddenPlayersWindow = window
end

-- Обновление данных в таблице скрытых игроков
function InterfaceModule:UpdateHiddenPlayersTable()
	if not self.hiddenPlayersWindow then
		return
	end

	local hiddenPlayers = {}
	local counter = 1

	-- Собираем данные о скрытых игроках
	for _, player in pairs(AlphaProtocol.ManagmentModule.playersToInvite) do
		if player.isHide then
			table.insert(hiddenPlayers, {
				countNumber = counter,
				name = player.name,
				Task = player.Task or "",
			})
			counter = counter + 1
		end
	end

	-- Обновляем данные в таблице
	self.hiddenPlayersWindow.scrollTable:SetData(hiddenPlayers)
end

-- Переключение видимости окна скрытых игроков
function InterfaceModule:ToggleHiddenPlayersWindow()
	if not self.hiddenPlayersWindow then
		self:CreateHiddenPlayersWindow()
	end

	if self.hiddenPlayersWindow:IsShown() then
		self.hiddenPlayersWindow:Hide()
	else
		self:UpdateHiddenPlayersTable()
		-- Синхронизируем уровень с mainWindow
		if self.mainWindow then
			self.hiddenPlayersWindow:SetFrameLevel(self.mainWindow:GetFrameLevel())
		end
		self.hiddenPlayersWindow:Show()
	end
end

-- Функция для установки максимального уровня фрейма
-- Используется для окон, которые не используют ToggleFrameVisibility
function InterfaceModule:SetTopFrameLevel(frame)
	if frame then
		frame:SetFrameLevel(self:GetNextFrameLevel())
	end
end

-- Функция добавления поддержки закрытия окна по клавише ESC
function InterfaceModule:EnableEscapeKey(frame)
	if not frame then return end
	
	-- Создаем уникальное имя для фрейма если его нет
	if not frame:GetName() then
		local name = "FIM_Frame_" .. tostring(frame)
		frame.frameType = "FRAME"
		_G[name] = frame
		frame:SetAttribute("NAME", name)
	end
	
	tinsert(UISpecialFrames, frame:GetName() or frame:GetAttribute("NAME"))
end

-- Функция для синхронизации уровней фреймов с mainWindow
function InterfaceModule:SyncFrameLevels()
	if not self.mainWindow then return end
	
	local level = self.mainWindow:GetFrameLevel()
	
	-- Синхронизируем уровни специальных окон
	if self.optionalWindow then
		self.optionalWindow:SetFrameLevel(level)
	end
	
	if self.sidebarWindow then
		self.sidebarWindow:SetFrameLevel(level)
	end
	
	if self.hiddenPlayersWindow then
		self.hiddenPlayersWindow:SetFrameLevel(level)
	end
end

-- Обновляет время последней синхронизации
function InterfaceModule:UpdateLastSyncTime()
	self.lastSyncTime = time()
	-- Сохраняем время в профиле аддона
	Main.db.profile.lastSyncTime = self.lastSyncTime
	self:DisplayLastSyncTime()
	
	-- Создаем или обновляем таймер для обновления текста каждую минуту
	if not self.syncUpdateTimer then
		self.syncUpdateTimer = C_Timer.NewTicker(5, function() 
			self:DisplayLastSyncTime()
		end)
	end
end

-- Отображает время, прошедшее с последней синхронизации
function InterfaceModule:DisplayLastSyncTime()
	if not self.lastSyncTime then return end
	
	-- Если нет главной кнопки, ничего не делаем
	if not self.mainButton then return end
	
	-- Создаем текстовое поле, если его еще нет
	if not self.syncTimeText then
		self.syncTimeText = self.mainButton:CreateFontString(nil, "OVERLAY", "GameFontNormalSmall")
		-- Меняем позицию с левой стороны на правую
		self.syncTimeText:SetPoint("LEFT", self.mainButton, "RIGHT", 5, 0)
	end
	
	-- Удаляем существующую анимацию, если она есть
	if self.syncTimeText.animation then
		self.syncTimeText.animation:Stop()
		self.syncTimeText.animation = nil
	end

	local currentTime = GetServerTime()
	local secondsElapsed = currentTime - self.lastSyncTime
	local minutesElapsed = math.floor(secondsElapsed / 60)
	local text
	
	-- Формируем текст в зависимости от прошедшего времени
	if minutesElapsed < 1 then
		-- Отображаем секунды
		if secondsElapsed == 1 then
			text = "last sync 1 second ago"
		else
			text = "last sync " .. secondsElapsed .. " seconds ago"
		end
	elseif minutesElapsed == 1 then
		text = "last sync 1 minute ago"
	else
		text = "last sync " .. minutesElapsed .. " minutes ago"
	end
	
	-- Применяем стили на основе времени синхронизации
	if secondsElapsed <= 14 then
		-- Первые 15 секунд: увеличенный шрифт и ярко-зеленый цвет
		self.syncTimeText:SetFont(self.syncTimeText:GetFont(), 18, "OUTLINE")
		self.syncTimeText:SetTextColor(0, 1, 0) -- Ярко-зеленый
		
		-- Создаем анимацию пульсации и мигающей обводки
		if not self.syncTimeText.animation then
			-- Создаем группу анимаций
			self.syncTimeText.animation = self.syncTimeText:CreateAnimationGroup()
			self.syncTimeText.animation:SetLooping("REPEAT")
			
			-- Анимация пульсации (масштабирование)
			local scale = self.syncTimeText.animation:CreateAnimation("Scale")
			scale:SetDuration(0.5)
			scale:SetOrder(1)
			scale:SetSmoothing("IN_OUT")
			scale:SetScaleFrom(1, 1)
			scale:SetScaleTo(1.2, 1.2)
			
			local scaleBack = self.syncTimeText.animation:CreateAnimation("Scale")
			scaleBack:SetDuration(0.5)
			scaleBack:SetOrder(2)
			scaleBack:SetSmoothing("IN_OUT")
			scaleBack:SetScaleFrom(1.2, 1.2)
			scaleBack:SetScaleTo(1, 1)
			
			-- Анимация мигающей обводки через изменение альфа-канала
			local fadeOut = self.syncTimeText.animation:CreateAnimation("Alpha")
			fadeOut:SetDuration(0.5)
			fadeOut:SetOrder(1)
			fadeOut:SetFromAlpha(1)
			fadeOut:SetToAlpha(0.5)
			
			local fadeIn = self.syncTimeText.animation:CreateAnimation("Alpha")
			fadeIn:SetDuration(0.5)
			fadeIn:SetOrder(2)
			fadeIn:SetFromAlpha(0.5)
			fadeIn:SetToAlpha(1)
			
			-- Запускаем анимацию
			self.syncTimeText.animation:Play()
		end
	elseif minutesElapsed < 10 then
		-- От 15 секунд до 10 минут: обычный размер и зеленый цвет
		self.syncTimeText:SetFont(self.syncTimeText:GetFont(), 12, "")
		self.syncTimeText:SetTextColor(0.3, 0.9, 0.3) -- Более спокойный зеленый
	else
		-- Более 10 минут: оставляем как сейчас
		self.syncTimeText:SetFont(self.syncTimeText:GetFont(), 10, "")
		self.syncTimeText:SetTextColor(0.7, 0.7, 0.7) -- Серый
	end
	
	self.syncTimeText:SetText(text)
	self.syncTimeText:Show()
end

local AlphaProtocol = LibStub("AceAddon-3.0"):GetAddon("FriendshipIsMagic")
local TaskConfigModule = AlphaProtocol:NewModule("TaskConfigModule", "AceEvent-3.0")
local StdUi = LibStub("StdUi")

-- Таблица значений по умолчанию
local defaultConfigs = {
    killAllBosses = {
        name = "Убить всех боссов",
        tasks = "MO Normal ATP 10 ppl,MO Normal ATP 20 ppl,MO Normal FP 10 ppl,MO Normal FP 20 ppl,MO Mythic 8/8 ATP,MO Heroic Premium,MO Heroic FP 15 ppl,MO Normal FP 15ppl,MO Heroic ATP 15ppl,MO Normal ATP 15ppl,MO Normal Unsaved,MO Heroic Unsaved,NP Normal Unsaved 4+items,NP Heroic Unsaved 4+items,NP Mythic,MO Normal,MO Heroic,MO Normal Unsaved 4+items,MO Heroic Unsaved 4+items,MO Heroic ATP 10ppl,MO Heroic ATP 20ppl,MO Heroic FP 10ppl,MO Mythic 8/8 NOLOOT ,MO Heroic FP 20ppl,MO Mythic 8/8 5items ATP 20ppl,MO Mythic 8/8 FP 20ppl,MO Mythic 8/8 NOLOOT"
    },
    killInfoBosses = {
        name = "Убить боссов из инфо",
        tasks = "Single Boss NM,Single Boss HC,NP Single Boss NM,NP Single Boss HC,MO Single Boss NM,MO Single Boss HC,MO Single Boss Mythic"
    },
    killLastBoss = {
        name = "Убить ласта",
        tasks = "Ansurek HC,Ansurek NM,Ansurek Heroic,Ansurek Normal,Ansurek Mythic,Gallywix Normal,Gallywix Heroic,Gallywix Mythic,Dimensius Normal,Dimensius Heroic,Dimensius Mythic"
    },
    partialRaid = {
        name = "Часть рейда",
        tasks = "NP Heroic */8,NP Normal */8,NP Mythic */8,MO Heroic */8,MO Normal */8,MO Mythic */8"
    }
}

function TaskConfigModule:OnEnable()
    -- Инициализация настроек по умолчанию, если они еще не созданы
    if not AlphaProtocol.db.global.TaskConfigs2 then
        AlphaProtocol.db.global.TaskConfigs2 = defaultConfigs
    end

    -- Добавляем метод проверки типа задания
    function self:GetTaskType(taskName)
        -- Сначала проверяем на частичный рейд
        if self:IsPartialRaidTask(taskName) then
            return "partialRaid"
        end
        
        -- Затем проверяем остальные типы
        for configType, config in pairs(AlphaProtocol.db.global.TaskConfigs2) do
            if configType ~= "partialRaid" then  -- Пропускаем проверку частичного рейда
                for _, task in ipairs({strsplit(",", config.tasks)}) do
                    if taskName:trim() == task:trim() then
                        return configType
                    end
                end
            end
        end
        return nil
    end

    -- Добавляем метод проверки частичного рейда
    function self:IsPartialRaidTask(taskName)
        if not taskName then return false end
      -- --print("DEBUG IsPartialRaidTask: Checking task:", taskName)
        
        local config = AlphaProtocol.db.global.TaskConfigs2.partialRaid
        if not config then 
         --  --print("DEBUG IsPartialRaidTask: No partial raid config found")
            return false 
        end
        
        for _, template in ipairs({strsplit(",", config.tasks)}) do
            template = template:trim()
          -- --print("DEBUG IsPartialRaidTask: Checking template:", template)
            
            -- Извлекаем базовое имя и общее количество боссов из шаблона
            local baseName, totalBosses = template:match("^(.+)%*/(%d+)$")
            if baseName and totalBosses then
                -- Очищаем базовое имя от пробелов в конце
                baseName = baseName:trim()
                -- Экранируем специальные символы в baseName
                baseName = baseName:gsub("([%(%)%.%%%+%-%*%?%[%]%^%$])", "%%%1")
             --  --print("DEBUG IsPartialRaidTask: Base name after escaping:", baseName)
                
                -- Создаем паттерн для проверки задания игрока
                local pattern = "^" .. baseName .. "%s*(%d+)/" .. totalBosses .. "$"
             --  --print("DEBUG IsPartialRaidTask: Created pattern:", pattern)
                
                -- Проверяем совпадение
                local match = taskName:match(pattern)
                if match then
             --      --print("DEBUG IsPartialRaidTask: Match found! Number:", match)
                    return true
                end
            end
        end
     --  --print("DEBUG IsPartialRaidTask: No match found")
        return false
    end

    -- Добавляем метод получения количества боссов для частичного рейда
    function self:GetPartialRaidBossCount(taskName)
        if not self:IsPartialRaidTask(taskName) then return nil end
        
        -- Извлекаем количество боссов из задания
        local bossCount = taskName:match("(%d+)/")
        if bossCount then
            return tonumber(bossCount)
        end
        return nil
    end
end

function TaskConfigModule:CloseSystemFrames()
    -- Закрываем только Settings панель через системную функцию
    if Settings and Settings.CloseUI then
        Settings.CloseUI()
    end
end

function TaskConfigModule:CreateConfigWindow()
    -- Закрываем все системные окна
    self:CloseSystemFrames()
    
    if self.configWindow then
        if self.configWindow:IsShown() then
            self.configWindow:Hide()
            return
        else
            -- Устанавливаем новый максимальный уровень фрейма при показе
            self.configWindow:SetFrameStrata("FULLSCREEN_DIALOG")
            self.configWindow:SetFrameLevel(100)
            self.configWindow:Raise()
            self.configWindow:Show()
            return
        end
    end

    local window = StdUi:Window(UIParent, 800, 400, "Настройка типов заданий")
    window:SetPoint("CENTER")
    window:SetFrameStrata("FULLSCREEN_DIALOG")
    window:SetFrameLevel(100)
    window:Raise()
    
    -- Добавляем обработчик для поддержания окна поверх других
    window:SetScript("OnShow", function(self)
        self:SetFrameStrata("FULLSCREEN_DIALOG")
        self:SetFrameLevel(100)
        self:Raise()
    end)
    
    self.configWindow = window

    -- Добавляем возможность закрытия окна по ESC
    AlphaProtocol.InterfaceModule:EnableEscapeKey(window)

    -- Создаем контейнер для вкладок
    local tabContainer = StdUi:Frame(window, window:GetWidth() - 40, window:GetHeight() - 60)
    tabContainer:SetPoint("TOPLEFT", window, "TOPLEFT", 20, -30)

    -- Создаем панель с кнопками вкладок
    local tabButtons = StdUi:Frame(tabContainer, tabContainer:GetWidth(), 30)
    tabButtons:SetPoint("TOPLEFT", tabContainer, "TOPLEFT", 0, 0)

    -- Создаем контейнер для содержимого вкладок
    local contentContainer = StdUi:Frame(tabContainer, tabContainer:GetWidth(), tabContainer:GetHeight() - 30)
    contentContainer:SetPoint("TOPLEFT", tabButtons, "BOTTOMLEFT", 0, -5)

    local tabs = {
        {
            name = "killAllBosses",
            title = "Убить всех боссов"
        },
        {
            name = "killInfoBosses",
            title = "Убить боссов из инфо"
        },
        {
            name = "killLastBoss",
            title = "Убить ласта"
        },
        {
            name = "partialRaid",
            title = "Часть рейда"
        }
    }

    local tabFrames = {}
    local tabButtons = {}
    local buttonWidth = 150
    local buttonHeight = 25
    local editBoxes = {} -- Добавляем таблицу для хранения всех editBox'ов

    -- Функция для переключения вкладок
    local function SelectTab(tabName)
        for name, frame in pairs(tabFrames) do
            if name == tabName then
                frame:Show()
            else
                frame:Hide()
            end
        end
        for _, button in pairs(tabButtons) do
            if button.tabName == tabName then
                button:SetBackdropColor(0.2, 0.4, 0.8, 0.9)
            else
                button:SetBackdropColor(0.1, 0.1, 0.1, 0.9)
            end
        end
    end

    -- Создаем содержимое для каждой вкладки
    for i, tab in ipairs(tabs) do
        -- Создаем кнопку вкладки
        local button = StdUi:Button(tabContainer, buttonWidth, buttonHeight, tab.title)
        button:SetPoint("TOPLEFT", tabContainer, "TOPLEFT", (i-1) * (buttonWidth + 5), 0)
        button.tabName = tab.name
        button:SetScript("OnClick", function() SelectTab(tab.name) end)
        table.insert(tabButtons, button)

        -- Создаем фрейм содержимого вкладки
        local frame = StdUi:Panel(contentContainer, contentContainer:GetWidth(), contentContainer:GetHeight())
        frame:SetPoint("TOPLEFT", contentContainer, "TOPLEFT", 0, 0)
        frame.layout = {}
        tabFrames[tab.name] = frame

        -- Добавляем описание в зависимости от типа вкладки
        local description
        if tab.name == "partialRaid" then
            description = "Введите шаблоны заданий в формате 'Название */N', где * будет заменено на количество боссов.\nПример: NP Heroic */8,NP Normal */8"
        else
            description = "Введите список тасков через запятую:"
        end

        local label = StdUi:Label(frame, description, 12)
        label:SetPoint("TOPLEFT", frame, "TOPLEFT", 10, -10)

        local editBox = StdUi:MultiLineBox(frame, frame:GetWidth() - 20, frame:GetHeight() - 80)
        editBox:SetPoint("TOPLEFT", label, "BOTTOMLEFT", 0, -5)
        editBoxes[tab.name] = editBox -- Сохраняем ссылку на editBox
        
        -- Загружаем текущие настройки
        local currentConfig = AlphaProtocol.db.global.TaskConfigs2[tab.name]
        if currentConfig then
            editBox:SetText(currentConfig.tasks)
        end

        -- Кнопка сохранения
        local saveButton = StdUi:Button(frame, 100, 30, "Сохранить")
        saveButton:SetPoint("TOPLEFT", editBox, "BOTTOMLEFT", 0, -10)
        saveButton:SetScript("OnClick", function()
            local tasks = editBox:GetText()
            -- Проверяем существование структуры данных
            if not AlphaProtocol.db.global.TaskConfigs2 then
                AlphaProtocol.db.global.TaskConfigs2 = {}
            end
            -- Проверяем существование конфигурации для данного типа
            if not AlphaProtocol.db.global.TaskConfigs2[tab.name] then
                AlphaProtocol.db.global.TaskConfigs2[tab.name] = {
                    name = tab.title,
                    tasks = ""
                }
            end
            -- Сохраняем новые данные
            AlphaProtocol.db.global.TaskConfigs2[tab.name].tasks = tasks
           print("Настройки сохранены для", tab.title)
        end)

        -- По умолчанию скрываем все вкладки кроме первой
        if i > 1 then
            frame:Hide()
        else
            button:SetBackdropColor(0.2, 0.4, 0.8, 0.9)
        end
    end

    -- Кнопка восстановления значений по умолчанию (общая для всех вкладок)
    local resetButton = StdUi:Button(contentContainer, 200, 30, "Восстановить по умолчанию")
    resetButton:SetPoint("BOTTOMRIGHT", contentContainer, "BOTTOMRIGHT", -10, 10)
    resetButton:SetBackdropColor(0.2, 0.4, 0.8, 0.9)
    resetButton:SetScript("OnClick", function()
        -- Восстанавливаем значения для всех вкладок
        for tabName, editBox in pairs(editBoxes) do
            if defaultConfigs[tabName] then
                editBox:SetText(defaultConfigs[tabName].tasks)
                -- Автоматически сохраняем восстановленные значения
                AlphaProtocol.db.global.TaskConfigs2[tabName].tasks = defaultConfigs[tabName].tasks
            end
        end
        print("Значения по умолчанию восстановлены для всех вкладок")
    end)
end 
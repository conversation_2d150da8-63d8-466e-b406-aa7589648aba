local AlphaProtocol = LibStub("AceAddon-3.0"):GetAddon("FriendshipIsMagic")
local ExportModule = AlphaProtocol:NewModule("ExportModule")
local Main = AlphaProtocol:GetModule("Main")

--[[
(raidID, raidDate, raidTime, raidTeam, playerID, playerName, serverName, playerTask, playerInfo,
    rlinfo, declinedTimestamp, noexistTimestamp, busyTimestamp, leaveTimestamp, bossKills, expiredTimestamps, doomsDay)
]]

-- КОСТЫЛЬ для исправления бага с отображением имени-сервера, который может включать в имя (имя сервер) а иногда нужны две переменные имя + сервер
-- Функция для формирования полного имени игрока
local function formatPlayerName(playerData)
	local fullName = playerData.playerName
	if playerData.serverName and playerData.serverName ~= "" then
		fullName = fullName .. "-" .. playerData.serverName
	end
	return fullName
end
------------------ конец костыля ----------------------------

-- Помогает вывести содержимое таблиц, вместо адреса
local function tableToString(tbl)
	if type(tbl) == "table" then
		local result = {}
		for key, value in pairs(tbl) do
			table.insert(result, tostring(value))
		end
		return table.concat(result, ", ")
	else
		return tostring(tbl)
	end
end

-- Финальный экспорт всей информации по игрокам
function ExportModule:FinalExport()
	local lastRaidID = Main.db.global.lastRaidID
	if not lastRaidID or not Main.db.global.RaidDB[lastRaidID] then
		return "Нет активного рейда для экспорта."
	end

	local raidData = Main.db.global.RaidDB[lastRaidID]

	local exportText = string.format(
		"raidID:%s; raidDate:%s; raidTime:%s; raidTeam:%s;\n",
		lastRaidID,
		raidData.raidDate or "",
		raidData.raidTime or "",
		raidData.raidTeam or ""
	)

	local playerText = ""

	for playerID, playerData in pairs(raidData.players) do
		-- Форматируем информацию о луте
		local lootInfo = ""
		if playerData.playerLoot and #playerData.playerLoot > 0 then
			local lootItems = {}
			for _, lootData in ipairs(playerData.playerLoot) do
				if lootData.raidID == lastRaidID then
					table.insert(lootItems, string.format("%s@%s", lootData.itemLink, lootData.timestamp or ""))
				end
			end
			if #lootItems > 0 then
				lootInfo = table.concat(lootItems, ",")
			end
		end

		local formattedText = string.format(
			"#playerID:%s; playerName:%s; playerTask:%s; playerInfo:%s; rlinfo:%s; "
				.. "leaveTimestamps:%s; noexistTimestamps:%s; declinedTimestamps:%s; "
				.. "busyTimestamps:%s; expiredTimestamps:%s; enemyTimestamps:%s; combatTimestamps:%s; "
				.. "bossKills:%s; playerLoot:%s;#\n",
			playerID or "",
			formatPlayerName(playerData),
			playerData.playerTask or "",
			playerData.playerInfo or "",
			playerData.rlinfo or "",
			table.concat(playerData.leaveTimestamps or {}, ", "),
			table.concat(playerData.noexistTimestamps or {}, ", "),
			table.concat(playerData.declinedTimestamps or {}, ", "),
			table.concat(playerData.busyTimestamps or {}, ", "),
			table.concat(playerData.expiredTimestamps or {}, ", "),
			table.concat(playerData.enemyTimestamps or {}, ", "),
			table.concat(playerData.combatTimestamps or {}, ", "),
			tableToString(playerData.bossKills or ""),
			lootInfo
		)
		playerText = playerText .. formattedText
	end

	-- Добавляем информацию о роллах
	local rollsText = "\n{ROLLS_DATA}\n"
	if raidData.lootRolls then
		for encounterID, encounterData in pairs(raidData.lootRolls) do
			rollsText = rollsText .. string.format("@encounterID:%d; encounterName:%s;\n", 
				encounterID, encounterData.encounterName)
			
			for _, itemData in ipairs(encounterData.items) do
				-- Форматируем информацию о победителе
				local winnerInfo = ""
				if itemData.winner then
					winnerInfo = string.format("winner:%s,%d,%d;", 
						itemData.winner.playerName, 
						itemData.winner.rollType, 
						itemData.winner.rollValue)
				end

				-- Форматируем информацию о роллах
				local rollsInfo = ""
				if itemData.rolls then
					local rollTypes = {}
					for rollType, players in pairs(itemData.rolls) do
						table.insert(rollTypes, string.format("type%d:%s", 
							rollType, table.concat(players, ",")))
					end
					rollsInfo = table.concat(rollTypes, ";")
				end

				-- Форматируем информацию о значениях роллов Need
				local needRollsInfo = ""
				if itemData.needRolls then
					local needRolls = {}
					for player, value in pairs(itemData.needRolls) do
						table.insert(needRolls, string.format("%s=%d", player, value))
					end
					needRollsInfo = "needRolls:" .. table.concat(needRolls, ",")
				end

				rollsText = rollsText .. string.format("#item:%s; %s %s; %s; timestamp:%d#\n",
					itemData.itemLink,
					winnerInfo,
					rollsInfo,
					needRollsInfo,
					itemData.timestamp or 0
				)
			end
		end
	end

	local finalText = exportText .. playerText .. rollsText .. "{END}"
	
	-- Очищаем lastRaidID
	Main:SetLastRaidID(nil)
	
	-- Очищаем список игроков
	wipe(AlphaProtocol.ManagmentModule.playersToInvite)
	
	-- Обновляем главное окно
	if AlphaProtocol.InterfaceModule.mainWindow then
		AlphaProtocol.ManagmentModule:UpdateMainWindow()
		AlphaProtocol.InterfaceModule:UpdateMainWindowRaidInfo()
	end
	if AlphaProtocol.InterfaceModule.hiddenPlayersWindow then
		AlphaProtocol.InterfaceModule:UpdateHiddenPlayersTable()
	end

	return finalText
end

-- Выгрузка проблемных игроков
function ExportModule:ProblemsExport()
	local lastRaidID = Main.db.global.lastRaidID

	if not lastRaidID or not Main.db.global.RaidDB[lastRaidID] then
		return "Нет данных о последнем рейде для экспорта."
	end

	local raidData = Main.db.global.RaidDB[lastRaidID]
	local playersWithProblems = {}

	-- Получаем текущий список игроков для приглашения для проверки статуса IN_RAID
	local currentPlayers = {}
	if AlphaProtocol.ManagmentModule and AlphaProtocol.ManagmentModule.playersToInvite then
		for _, playerInfo in ipairs(AlphaProtocol.ManagmentModule.playersToInvite) do
			if playerInfo.playerID then
				currentPlayers[playerInfo.playerID] = playerInfo.status
			end
		end
	end

	for playerID, playerData in pairs(raidData.players) do
		local playerName = formatPlayerName(playerData)
		local lastProblemType = nil
		local lastProblemTime = nil
		local hasProblems = false

		-- Функция для парсинга времени из таймштампа (формат HH:MM)
		local function parseTime(timestamp)
			if not timestamp then return nil end
			local hour, min = timestamp:match("(%d+):(%d+)")
			if hour and min then
				return tonumber(hour) * 60 + tonumber(min) -- Конвертируем в минуты для сравнения
			end
			return nil
		end

		-- Функция для проверки и обновления последней проблемы по времени
		local function checkProblemTimestamps(problemType, timestamps)
			if timestamps and #timestamps > 0 then
				hasProblems = true
				-- Берем последний таймштамп из списка (самый новый)
				local latestTimestamp = timestamps[#timestamps]
				local problemTime = parseTime(latestTimestamp)

				if problemTime and (not lastProblemTime or problemTime > lastProblemTime) then
					lastProblemTime = problemTime
					lastProblemType = problemType
				end
			end
		end

		-- Проверяем все типы проблем для поиска последней по времени
		checkProblemTimestamps("Left Group", playerData.leaveTimestamps)
		checkProblemTimestamps("Offline/Incorrect", playerData.noexistTimestamps)
		checkProblemTimestamps("Declined", playerData.declinedTimestamps)
		checkProblemTimestamps("Busy/In Group", playerData.busyTimestamps)
		checkProblemTimestamps("Expired", playerData.expiredTimestamps)
		checkProblemTimestamps("Enemy Faction", playerData.enemyTimestamps)
		checkProblemTimestamps("Raid Combat", playerData.combatTimestamps)

		-- Проверяем, находится ли игрок в рейде (статус IN_RAID)
		local isInRaid = currentPlayers[playerID] == AlphaProtocol.ManagmentModule.strings.IN_RAID

		if hasProblems and not isInRaid then
			-- Показываем только игроков с проблемами, которые не в рейде
			table.insert(playersWithProblems, string.format("%s %s %s", playerID, playerName, lastProblemType))
		end
		-- Игроки с решенными проблемами и игроки без проблем не отображаются
	end

	return table.concat(playersWithProblems, "\n")
end

-- Выгрузка всех клиентов рейда
function ExportModule:CopyClientsExport()
	local lastRaidID = Main.db.global.lastRaidID

	if not lastRaidID or not Main.db.global.RaidDB[lastRaidID] then
		return "Нет данных о последнем рейде для экспорта."
	end

	local raidData = Main.db.global.RaidDB[lastRaidID]
	local clientsInRaidFromList = {}
	local clientsInRaidNotInList = {}

	-- Получаем текущий список игроков для приглашения
	local currentPlayers = {}
	if AlphaProtocol.ManagmentModule and AlphaProtocol.ManagmentModule.playersToInvite then
		for _, playerInfo in ipairs(AlphaProtocol.ManagmentModule.playersToInvite) do
			if playerInfo.playerID then
				currentPlayers[playerInfo.playerID] = {
					status = playerInfo.status,
					name = playerInfo.name
				}
			end
		end
	end

	-- Получаем список всех игроков в рейде
	local raidMembers = {}
	local raidMembersAfterGroup4 = {}
	if IsInRaid() then
		for i = 1, GetNumGroupMembers() do
			local name, _, subGroup = GetRaidRosterInfo(i)
			if name then
				-- Обеспечиваем полное имя с сервером
				if not name:find("-") then
					local serverName = GetRealmName():gsub(" ", "")
					name = name .. "-" .. serverName
				end
				raidMembers[name:lower()] = name

				-- Игроки после 4 группы
				if subGroup > 4 then
					raidMembersAfterGroup4[name:lower()] = name
				end
			end
		end
	end

	-- 1. Игроки из списка рейда - проверяем реальное присутствие в рейде
	for playerID, playerData in pairs(raidData.players) do
		local playerName = formatPlayerName(playerData)
		local playerNameLower = playerName:lower()

		-- Проверяем, действительно ли игрок в рейде
		local isActuallyInRaid = raidMembers[playerNameLower] ~= nil

		if isActuallyInRaid then
			-- Проверяем онлайн статус игрока в рейде
			local isOnline = false
			for i = 1, GetNumGroupMembers() do
				local name, _, _, _, _, _, _, online = GetRaidRosterInfo(i)
				if name then
					-- Обеспечиваем полное имя с сервером для сравнения
					if not name:find("-") then
						local serverName = GetRealmName():gsub(" ", "")
						name = name .. "-" .. serverName
					end

					if name:lower() == playerNameLower then
						isOnline = online
						break
					end
				end
			end

			if isOnline then
				table.insert(clientsInRaidFromList, string.format("%s %s :white_check_mark:", playerID, playerName))
			else
				table.insert(clientsInRaidFromList, string.format("%s %s [в рейде но оффлайн]", playerID, playerName))
			end
		end
	end

	-- 2. Игроки в рейде после 4 группы, которых нет в списке нашего рейда
	for nameLower, fullName in pairs(raidMembersAfterGroup4) do
		local foundInList = false
		for _, playerInfo in pairs(currentPlayers) do
			if playerInfo.name:lower() == nameLower then
				foundInList = true
				break
			end
		end

		if not foundInList then
			-- Проверяем онлайн статус игрока
			local isOnline = false
			for i = 1, GetNumGroupMembers() do
				local name, _, subGroup, _, _, _, _, online = GetRaidRosterInfo(i)
				if name and subGroup > 4 then
					-- Обеспечиваем полное имя с сервером для сравнения
					if not name:find("-") then
						local serverName = GetRealmName():gsub(" ", "")
						name = name .. "-" .. serverName
					end

					if name:lower() == nameLower then
						isOnline = online
						break
					end
				end
			end

			if isOnline then
				table.insert(clientsInRaidNotInList, string.format("(ника нет в списке) %s :white_check_mark:", fullName))
			else
				table.insert(clientsInRaidNotInList, string.format("(ника нет в списке) %s [в рейде но оффлайн]", fullName))
			end
		end
	end

	-- Объединяем списки
	local allClients = {}
	for _, client in ipairs(clientsInRaidFromList) do
		table.insert(allClients, client)
	end
	for _, client in ipairs(clientsInRaidNotInList) do
		table.insert(allClients, client)
	end

	return table.concat(allClients, "\n")
end

function ExportModule:GetRaidListForDropdown()
	local raidDB = Main.db.global.RaidDB
	local items = {}
	for raidID, raidInfo in pairs(raidDB) do
		local displayText = string.format("Raid %s", raidID)
		table.insert(items, {
			text = displayText,
			value = raidID,
		})
	end
	-- Сортируем рейды по убыванию (новые сверху)
	table.sort(items, function(a, b)
		return a.value > b.value
	end)
	return items
end

-- Общий экспорт всех рейдов через Историю рейдов
function ExportModule:HistoryExport(raidID, infoBox)
	local raidData = Main.db.global.RaidDB[raidID]
	if not raidData then
		infoBox:SetText("Raid information not found")
		return
	end

	local exportText = string.format(
		"raidID:%s; raidDate:%s; raidTime:%s; raidTeam:%s;\n",
		raidID,
		raidData.raidDate or "",
		raidData.raidTime or "",
		raidData.raidTeam or ""
	)

	local playerText = ""

	for playerID, playerData in pairs(raidData.players) do
		-- Форматируем информацию о луте
		local lootInfo = ""
		if playerData.playerLoot and #playerData.playerLoot > 0 then
			local lootItems = {}
			for _, lootData in ipairs(playerData.playerLoot) do
				if lootData.raidID == raidID then
					table.insert(lootItems, string.format("%s@%s", lootData.itemLink, lootData.timestamp or ""))
				end
			end
			if #lootItems > 0 then
				lootInfo = table.concat(lootItems, ",")
			end
		end

		local formattedText = string.format(
			"#playerID:%s; playerName:%s; playerTask:%s; playerInfo:%s; rlinfo:%s; "
				.. "leaveTimestamps:%s; noexistTimestamps:%s; declinedTimestamps:%s; "
				.. "busyTimestamps:%s; expiredTimestamps:%s; bossKills:%s; playerLoot:%s;#\n",
			playerID,
			formatPlayerName(playerData),
			playerData.playerTask or "",
			playerData.playerInfo or "",
			playerData.rlinfo or "",
			table.concat(playerData.leaveTimestamps or {}, ", "),
			table.concat(playerData.noexistTimestamps or {}, ", "),
			table.concat(playerData.declinedTimestamps or {}, ", "),
			table.concat(playerData.busyTimestamps or {}, ", "),
			table.concat(playerData.expiredTimestamps or {}, ", "),
			tableToString(playerData.bossKills or ""),
			lootInfo
		)
		playerText = playerText .. formattedText
	end

	-- Добавляем информацию о роллах
	local rollsText = "\n{ROLLS_DATA}\n"
	if raidData.lootRolls then
		for encounterID, encounterData in pairs(raidData.lootRolls) do
			rollsText = rollsText .. string.format("@encounterID:%d; encounterName:%s;\n", 
				encounterID, encounterData.encounterName)
			
			for _, itemData in ipairs(encounterData.items) do
				-- Форматируем информацию о победителе
				local winnerInfo = ""
				if itemData.winner then
					winnerInfo = string.format("winner:%s,%d,%d;", 
						itemData.winner.playerName, 
						itemData.winner.rollType, 
						itemData.winner.rollValue)
				end

				-- Форматируем информацию о роллах
				local rollsInfo = ""
				if itemData.rolls then
					local rollTypes = {}
					for rollType, players in pairs(itemData.rolls) do
						table.insert(rollTypes, string.format("type%d:%s", 
							rollType, table.concat(players, ",")))
					end
					rollsInfo = table.concat(rollTypes, ";")
				end

				-- Форматируем информацию о значениях роллов Need
				local needRollsInfo = ""
				if itemData.needRolls then
					local needRolls = {}
					for player, value in pairs(itemData.needRolls) do
						table.insert(needRolls, string.format("%s=%d", player, value))
					end
					needRollsInfo = "needRolls:" .. table.concat(needRolls, ",")
				end

				rollsText = rollsText .. string.format("#item:%s; %s %s; %s; timestamp:%d#\n",
					itemData.itemLink,
					winnerInfo,
					rollsInfo,
					needRollsInfo,
					itemData.timestamp or 0
				)
			end
		end
	end

	local finalText = exportText .. playerText .. rollsText .. "{END}"
	-- Устанавливаем собранный текст в infoBox
	infoBox:SetText(finalText)
end
